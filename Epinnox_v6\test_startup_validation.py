#!/usr/bin/env python3
"""
Startup Validation Test for Epinnox v6 Trading System
Tests that all critical components can be imported and initialized
"""

import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_critical_imports():
    """Test that all critical components can be imported"""
    print("🔍 Testing critical imports...")
    
    try:
        # Test main launch module
        print("  ✓ Testing launch_epinnox imports...")
        from typing import Dict, Any, List, Optional
        print("    ✓ Type hints imported successfully")
        
        # Test advanced systems
        print("  ✓ Testing advanced trading systems...")
        from core.advanced_microstructure import AdvancedMicrostructureAnalyzer
        from core.regime_detector import RegimeDetector
        from core.volatility_pause_system import VolatilityPauseSystem
        from core.prompt_optimizer import PromptOptimizer
        print("    ✓ All advanced systems imported successfully")
        
        # Test system initialization
        print("  ✓ Testing system initialization...")
        microstructure = AdvancedMicrostructureAnalyzer()
        regime_detector = RegimeDetector()
        volatility_system = VolatilityPauseSystem()
        prompt_optimizer = PromptOptimizer()
        print("    ✓ All systems initialized successfully")
        
        # Test data requirements
        print("  ✓ Testing data requirements...")
        print(f"    - Volatility min candles: {volatility_system.MIN_CANDLES_VOLATILITY}")
        print(f"    - Regime min candles: {regime_detector.MINIMUM_CANDLES}")
        print(f"    - Microstructure min trades: {microstructure.MIN_TRADE_HISTORY}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import/initialization failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_data_requirements_validation():
    """Test data requirements validation logic"""
    print("\n🔍 Testing data requirements validation...")
    
    try:
        from core.volatility_pause_system import VolatilityPauseSystem
        from core.regime_detector import RegimeDetector
        from core.advanced_microstructure import AdvancedMicrostructureAnalyzer
        
        # Test with insufficient data
        insufficient_data = {
            'candles_1m': [{'close': 100}] * 10,  # Only 10 candles
            'candles_5m': [{'close': 100}] * 10,
            'candles_15m': [{'close': 100}] * 10,
            'recent_trades': [{'price': 100, 'amount': 1}] * 10
        }
        
        volatility_system = VolatilityPauseSystem()
        regime_detector = RegimeDetector()
        microstructure = AdvancedMicrostructureAnalyzer()
        
        # Test volatility system validation
        vol_result = volatility_system.analyze_volatility("TEST/USDT", insufficient_data)
        print(f"  ✓ Volatility system handles insufficient data: {vol_result is not None}")
        
        # Test regime detector validation
        regime_result = regime_detector.detect_regime("TEST/USDT", insufficient_data)
        print(f"  ✓ Regime detector handles insufficient data: {regime_result is None}")
        
        # Test microstructure validation
        micro_result = microstructure.analyze_microstructure("TEST/USDT", insufficient_data)
        print(f"  ✓ Microstructure handles insufficient data: {micro_result is None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data validation test failed: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_graceful_degradation():
    """Test graceful degradation logic"""
    print("\n🔍 Testing graceful degradation...")
    
    try:
        # This would normally be in launch_epinnox.py, but we'll test the concept
        test_data = {
            'candles': [{'close': 100}] * 30,     # Insufficient for regime (need 60)
            'candles_5m': [{'close': 100}] * 30,  # Insufficient for volatility (need 60)
            'candles_15m': [{'close': 100}] * 20, # Insufficient for regime (need 32)
            'recent_trades': [{'price': 100}] * 50 # Insufficient for microstructure (need 100)
        }
        
        # Simulate degradation logic
        volatility_ready = len(test_data.get('candles_5m', [])) >= 60
        regime_ready = (len(test_data.get('candles', [])) >= 60 and 
                       len(test_data.get('candles_5m', [])) >= 48 and 
                       len(test_data.get('candles_15m', [])) >= 32)
        microstructure_ready = len(test_data.get('recent_trades', [])) >= 100
        
        print(f"  ✓ Volatility ready: {volatility_ready}")
        print(f"  ✓ Regime ready: {regime_ready}")
        print(f"  ✓ Microstructure ready: {microstructure_ready}")
        
        if all([volatility_ready, regime_ready, microstructure_ready]):
            mode = "FULL_ADVANCED"
        elif volatility_ready and regime_ready:
            mode = "REDUCED"
        else:
            mode = "BASIC_SCALPING"
        
        print(f"  ✓ Degradation mode selected: {mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Graceful degradation test failed: {e}")
        return False

def main():
    """Run all validation tests"""
    print("🚀 Epinnox v6 Startup Validation Test")
    print("=" * 50)
    
    tests = [
        ("Critical Imports", test_critical_imports),
        ("Data Requirements", test_data_requirements_validation),
        ("Graceful Degradation", test_graceful_degradation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            if test_func():
                print(f"✅ {test_name} Test PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - System ready for startup!")
        print("\n🚀 Next Steps:")
        print("1. Run: python launch_epinnox.py")
        print("2. Verify GUI loads without errors")
        print("3. Check that advanced systems initialize")
        print("4. Test data fetching with enhanced limits")
        return True
    else:
        print("⚠️  Some tests failed - check errors above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
