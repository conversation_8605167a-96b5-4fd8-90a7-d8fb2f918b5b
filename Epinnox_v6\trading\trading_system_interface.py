from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThreadPool, QRunnable
import time
import json
import os
import random
import yaml
import logging
from datetime import datetime, timedelta

# Import shared data manager
from utils.shared_data_manager import SharedDataManager

# Import trading system components
# Note: Removed import of run_trading_system to avoid circular imports
from core.trade_lifecycle import TradeManager
from core.dynamic_targets import DynamicTargetCalculator
from core.signal_scoring import SignalScorer
from data.exchange import ExchangeDataFetcher
from config.config import EXCHANGE_ID, EXCHANGE_API_KEY, EXCHANGE_SECRET

# Configure logging
logger = logging.getLogger(__name__)

class SimulatedAccount:
    """
    Simulated trading account for testing.
    """
    def __init__(self, initial_balance=500.0, currency='USDT'):
        """
        Initialize the simulated account.

        Args:
            initial_balance: Initial account balance
            currency: Account currency
        """
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.currency = currency
        self.positions = {}
        self.balance_history = {}
        self.trade_history = []
        self.record_balance()

    def record_balance(self):
        """
        Record current balance.
        """
        # Use timestamp as key for easy lookup
        timestamp = datetime.now().timestamp()
        self.balance_history[timestamp] = self.balance

    def open_position(self, symbol, side, amount, price, leverage=1):
        """
        Open a trading position.

        Args:
            symbol: Trading symbol
            side: Trade side ('buy' or 'sell')
            amount: Trade amount
            price: Entry price
            leverage: Leverage multiplier

        Returns:
            dict: Position information
        """
        # Calculate position value
        position_value = amount * price

        # Calculate margin required (position value / leverage)
        margin = position_value / leverage

        # Check if we have enough balance
        if margin > self.balance:
            return None

        # Generate position ID
        position_id = len(self.positions) + 1

        # Create position
        position = {
            'id': position_id,
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'entry_price': price,
            'leverage': leverage,
            'margin': margin,
            'position_value': position_value,
            'timestamp': datetime.now(),
            'status': 'open',
            'unrealized_pnl': 0.0,
            'exit_price': None,
            'exit_timestamp': None,
            'realized_pnl': None
        }

        # Add to positions
        self.positions[position_id] = position

        # Deduct margin from balance
        self.balance -= margin
        self.record_balance()

        # Record trade
        self.trade_history.append({
            'timestamp': datetime.now(),
            'type': 'open',
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'price': price,
            'leverage': leverage,
            'margin': margin,
            'balance_after': self.balance
        })

        return position

    def update_position(self, position_id, current_price):
        """
        Update position with current price.

        Args:
            position_id: Position ID
            current_price: Current price

        Returns:
            dict: Updated position
        """
        if position_id not in self.positions:
            return None

        position = self.positions[position_id]

        if position['status'] != 'open':
            return position

        # Calculate unrealized P&L
        if position['side'] == 'buy':
            pnl = (current_price - position['entry_price']) * position['amount'] * position['leverage']
        else:
            pnl = (position['entry_price'] - current_price) * position['amount'] * position['leverage']

        # Update position
        position['unrealized_pnl'] = pnl

        return position

    def close_position(self, position_id, exit_price):
        """
        Close a trading position.

        Args:
            position_id: Position ID
            exit_price: Exit price

        Returns:
            dict: Closed position
        """
        if position_id not in self.positions:
            return None

        position = self.positions[position_id]

        if position['status'] != 'open':
            return position

        # Calculate realized P&L
        if position['side'] == 'buy':
            pnl = (exit_price - position['entry_price']) * position['amount'] * position['leverage']
        else:
            pnl = (position['entry_price'] - exit_price) * position['amount'] * position['leverage']

        # Update position
        position['exit_price'] = exit_price
        position['exit_timestamp'] = datetime.now()
        position['realized_pnl'] = pnl
        position['status'] = 'closed'

        # Return margin + P&L to balance
        self.balance += position['margin'] + pnl
        self.record_balance()

        # Record trade
        self.trade_history.append({
            'timestamp': datetime.now(),
            'type': 'close',
            'symbol': position['symbol'],
            'side': 'sell' if position['side'] == 'buy' else 'buy',
            'amount': position['amount'],
            'price': exit_price,
            'pnl': pnl,
            'balance_after': self.balance
        })

        return position

    def get_balance(self):
        """
        Get current balance.

        Returns:
            float: Current balance
        """
        return self.balance

    def get_equity(self):
        """
        Get current equity (balance + unrealized P&L).

        Returns:
            float: Current equity
        """
        unrealized_pnl = sum(p['unrealized_pnl'] for p in self.positions.values() if p['status'] == 'open')
        return self.balance + unrealized_pnl

    def get_positions(self):
        """
        Get all positions.

        Returns:
            dict: All positions
        """
        return self.positions

    def get_open_positions(self):
        """
        Get open positions.

        Returns:
            dict: Open positions
        """
        return {k: v for k, v in self.positions.items() if v['status'] == 'open'}

    def get_balance_history(self):
        """
        Get balance history.

        Returns:
            list: Balance history
        """
        return self.balance_history

    def get_trade_history(self):
        """
        Get trade history.

        Returns:
            list: Trade history
        """
        return self.trade_history


class TradingSystemInterface(QObject):
    """Interface between the UI and the trading system"""

    # Signals
    data_updated = pyqtSignal(dict)
    log_message = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.trading_active = False
        self.parameters = self.load_parameters()
        self.current_state = {
            'connection_status': 'Disconnected',
            'last_update': 'Never',
            'account_balance': 0.0,
            'active_positions': [],
            'position_history': [],
            'signal_data': {},
            'performance_data': {},
            'regime_data': {},
            'timeframe_data': {},
            'log_messages': []
        }

        # Check if we should use a real exchange or a simulated account
        self.use_real_exchange = self.parameters.get('use_real_exchange', True)

        if self.use_real_exchange:
            # Try to load credentials from file
            try:
                credentials = self.load_credentials()
                api_key = credentials.get('apiKey', EXCHANGE_API_KEY)
                secret = credentials.get('secret', EXCHANGE_SECRET)
                exchange_id = credentials.get('exchange', EXCHANGE_ID)

                # Initialize exchange data fetcher
                self.exchange = ExchangeDataFetcher(exchange_id=exchange_id, api_key=api_key, secret=secret)

                # Fetch futures account balance
                try:
                    # Use the futures balance instead of spot balance
                    self.account_balance = self.exchange.fetch_futures_balance('USDT')
                    self.current_state['account_balance'] = self.account_balance
                    self.current_state['connection_status'] = 'Connected'
                    logger.info(f"Fetched futures account balance: {self.account_balance} USDT")

                    # Create a real account object that mimics the SimulatedAccount interface
                    class RealAccount:
                        def __init__(self, balance, currency='USDT'):
                            self.balance = balance
                            self.currency = currency
                            self.open_positions = {}

                        def get_balance(self):
                            return self.balance

                        def update_balance(self, amount):
                            self.balance += amount

                        def get_open_positions(self):
                            return self.open_positions

                        def close_position(self, position_id, exit_price):
                            if position_id in self.open_positions:
                                del self.open_positions[position_id]
                                return True
                            return False

                    # Initialize the account with the real balance
                    self.account = RealAccount(self.account_balance)
                except Exception as e:
                    logger.error(f"Error fetching account balance: {e}")
                    # Fall back to simulated account
                    self.use_real_exchange = False
            except Exception as e:
                logger.error(f"Error loading credentials: {e}")
                # Fall back to simulated account
                self.use_real_exchange = False

        # Initialize simulated account if not using real exchange
        if not self.use_real_exchange:
            self.account = SimulatedAccount(initial_balance=self.parameters.get('initial_balance', 500.0))
            logger.info(f"Using simulated account with balance: {self.account.balance} {self.account.currency}")

        # Initialize shared data manager
        self.data_manager = SharedDataManager()

        # Register for data updates
        self.data_manager.data_updated.connect(self.on_data_updated)

        # Initialize trade manager
        self.trade_manager = None
        try:
            self.trade_manager = TradeManager(save_dir='trades')
            self.dynamic_target_calculator = DynamicTargetCalculator(exchange_id='htx')
            self.signal_scorer = SignalScorer()

            # Calculate initial dynamic targets
            self.dynamic_targets = self.dynamic_target_calculator.calculate_targets(
                symbol=self.parameters.get('symbol', 'DOGE/USDT'),
                leverage=self.parameters.get('leverage', 5)
            )

            # Log initialization
            self.log_message.emit({
                'level': 'info',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Initialized trading system for {self.parameters.get('symbol', 'DOGE/USDT')}"
            })
        except Exception as e:
            print(f"Error initializing trading components: {e}")

        # Initialize thread pool
        self.threadpool = QThreadPool()

        # Start update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_data)
        self.update_timer.start(1000)  # Update every second

        # Start decision timer
        self.decision_timer = QTimer()
        self.decision_timer.timeout.connect(self.periodic_decision)
        self.decision_timer.start(60000)  # Make trading decision every minute

    def load_parameters(self):
        """Load trading parameters from file"""
        try:
            if os.path.exists('trading_parameters.json'):
                with open('trading_parameters.json', 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading parameters: {e}")

        # Default parameters
        return {
            'symbol': 'DOGE/USDT:USDT',
            'timeframe': '1m',
            'leverage': 20,
            'position_size': 10,
            'min_confidence': 65,
            'trend_alignment': 0.6,
            'signal_weights': {
                'macd': 0.3,
                'orderbook': 0.2,
                'volume': 0.25,
                'price_action': 0.25
            },
            'take_profit': 5.0,
            'stop_loss': 3.0,
            'use_real_exchange': True,  # Set to True to use real exchange instead of simulation
            'trailing_stop': {
                'enabled': True,
                'value': 1.0
            },
            'partial_tp': {
                'enabled': False,
                'percentage': 50
            },
            'breakeven': {
                'enabled': True,
                'after_profit': 0.5
            }
        }

    def save_parameters(self):
        """Save trading parameters to file"""
        try:
            with open('trading_parameters.json', 'w') as f:
                json.dump(self.parameters, f, indent=4)
        except Exception as e:
            print(f"Error saving parameters: {e}")
            self.log_message.emit({
                'level': 'error',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Error saving parameters: {e}"
            })

    def update_parameters(self, params):
        """Update trading parameters"""
        # Update parameters
        for key, value in params.items():
            self.parameters[key] = value

        # Save parameters
        self.save_parameters()

        # Log parameter update
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Updated trading parameters: {', '.join(params.keys())}"
        })

        # Apply parameters to trading system if active
        if self.trading_active:
            self.apply_parameters()

    def apply_parameters(self):
        """Apply parameters to the trading system"""
        # This would connect to your actual trading system
        # For now, just log the action
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Applied parameters to trading system: {self.parameters['symbol']}, Leverage: {self.parameters['leverage']}x"
        })

    def load_credentials(self):
        """Load credentials from file"""
        # Try to load from credentials.yaml
        credentials_path = os.path.join('config', 'credentials.yaml')
        if os.path.exists(credentials_path):
            try:
                with open(credentials_path, 'r') as f:
                    credentials = yaml.safe_load(f)
                logger.info(f"Loaded credentials from {credentials_path}")
                return credentials
            except Exception as e:
                logger.error(f"Error loading credentials from {credentials_path}: {e}")

        # Fall back to config values
        return {
            'exchange': EXCHANGE_ID,
            'apiKey': EXCHANGE_API_KEY,
            'secret': EXCHANGE_SECRET
        }

    def start_trading(self):
        """Start the trading system"""
        print("\n\n*** TRADING SYSTEM INTERFACE - START_TRADING METHOD CALLED ***\n\n")

        if self.trading_active:
            print("Trading already active, ignoring start request")
            return

        # Set trading active
        self.trading_active = True
        print(f"Set trading_active to {self.trading_active}")

        # Update connection status
        self.current_state['connection_status'] = 'Connected'
        print(f"Updated connection status to {self.current_state['connection_status']}")

        # Log start
        print("Emitting log message for trading start")
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Trading started for {self.parameters['symbol']}"
        })

        # Apply parameters
        print("Applying trading parameters")
        self.apply_parameters()

        # Generate initial performance data
        print("Generating initial performance data")
        current_price = 0.15  # Default price if we can't get a real one
        # Create empty performance data
        performance_data = {
            'equity_data': [],
            'trade_history': [],
            'trade_markers': [],
            'summary': 'No trading data available yet'
        }

        # Update current state with performance data
        self.current_state['performance_data'] = performance_data

        # Emit data updated signal
        print("Emitting data_updated signal with performance data")
        self.data_updated.emit(self.current_state)

        # Start trading decision process
        print("Starting trading decision process")
        self.make_trading_decision()

        print("\n*** START_TRADING METHOD COMPLETED ***\n")

    def stop_trading(self):
        """Stop the trading system"""
        if not self.trading_active:
            return

        # Set trading inactive
        self.trading_active = False

        # Update connection status
        self.current_state['connection_status'] = 'Disconnected'

        # Log stop
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Trading stopped for {self.parameters['symbol']}"
        })

    def is_trading_active(self):
        """Check if trading is active"""
        return self.trading_active

    def make_trading_decision(self):
        """Make a trading decision based on current market data"""
        if not self.trading_active or not self.trade_manager:
            return

        # Get symbol from parameters
        symbol = self.parameters.get('symbol', 'DOGE/USDT')

        # Log decision process
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Making trading decision for {symbol}"
        })

        try:
            # Import here to avoid circular imports
            from core.main import run_trading_system

            # Run trading system to get decision
            decision, explanation, parsed_response = run_trading_system(
                symbol=symbol,
                interval='1m',
                period='1d',
                use_exchange=True,
                use_live_data=True
            )

            # Extract model confidence and TP/SL if available
            model_confidence = parsed_response.get('confidence')
            model_take_profit = parsed_response.get('take_profit')
            model_stop_loss = parsed_response.get('stop_loss')

            # Get signal scores
            signal_scores = parsed_response.get('signal_scores', {})

            # Get current price from data manager
            combined_data = self.data_manager.get_data(symbol, 'combined_data')
            features = self.data_manager.get_data(symbol, 'features')

            current_price = features.get('spot_close', 0) or features.get('futures_close', 0)
            if not current_price and combined_data.get('spot_orderbook', {}).get('mid_price'):
                current_price = combined_data['spot_orderbook']['mid_price']

            # Log decision
            self.log_message.emit({
                'level': 'info',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Decision: {decision}, Confidence: {model_confidence}%, Price: {current_price}"
            })

            # Update UI with decision
            self.current_state['last_decision'] = {
                'decision': decision,
                'explanation': explanation,
                'confidence': model_confidence,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'price': current_price
            }

            # Update signal data
            if signal_scores:
                self.current_state['signal_data'] = signal_scores

            # Update regime data
            if 'market_regime' in parsed_response:
                self.current_state['regime_data'] = parsed_response.get('market_regime', {})

            # Update timeframe data
            if 'multi_timeframe_analysis' in parsed_response:
                self.current_state['timeframe_data'] = parsed_response.get('multi_timeframe_analysis', {})

            # Emit updated state
            self.data_updated.emit(self.current_state)

            # Execute trade if decision is not WAIT
            if decision not in ["WAIT", "ERROR"] and self.parameters.get('auto_trade', False):
                self.execute_trade(decision, current_price, model_confidence, model_take_profit, model_stop_loss)

        except Exception as e:
            self.log_message.emit({
                'level': 'error',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Error making trading decision: {e}"
            })
            print(f"Error making trading decision: {e}")

    def execute_trade(self, decision, current_price, confidence=None, take_profit=None, stop_loss=None):
        """Execute a trade based on the decision"""
        if not self.trading_active or not self.trade_manager:
            return

        # Get symbol and parameters
        symbol = self.parameters.get('symbol', 'DOGE/USDT')
        leverage = self.parameters.get('leverage', 5)
        risk_percentage = self.parameters.get('risk_percentage', 2.0)
        max_position_size = self.parameters.get('max_position_size', 100.0)

        # Calculate position size
        try:
            # Get available balance
            available_balance = self.account.get_balance()

            # Calculate position size using dynamic calculator
            position_size = self.dynamic_target_calculator.calculate_position_size(
                symbol=symbol,
                available_balance=available_balance,
                current_price=current_price,
                leverage=leverage,
                risk_percentage=risk_percentage
            )

            # Ensure position size doesn't exceed max position size
            position_size_usd = position_size * current_price
            if position_size_usd > max_position_size:
                position_size = max_position_size / current_price
                self.log_message.emit({
                    'level': 'info',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'message': f"Position size reduced to respect max position size: {max_position_size} USD"
                })
        except Exception as e:
            # Fall back to simple calculation
            self.log_message.emit({
                'level': 'warning',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Dynamic position sizing failed: {e}. Using simple calculation."
            })
            position_size_usd = min(available_balance * risk_percentage / 100, max_position_size)
            position_size = position_size_usd / current_price

        # Determine trade side
        side = 'LONG' if decision == 'LONG' else 'SHORT'

        # Calculate take profit and stop loss prices if not provided
        if not take_profit or not stop_loss:
            # Use dynamic targets
            dynamic_targets = self.dynamic_target_calculator.calculate_targets(
                symbol=symbol,
                leverage=leverage,
                confidence=confidence/100 if confidence else 0.7
            )

            # Get the dynamic targets
            tp_percentage = take_profit if take_profit else dynamic_targets.get('take_profit', 5.0)
            sl_percentage = stop_loss if stop_loss else dynamic_targets.get('stop_loss', 2.0)
        else:
            tp_percentage = take_profit
            sl_percentage = stop_loss

        # Calculate take profit and stop loss prices
        if side == 'LONG':
            take_profit_price = current_price * (1 + tp_percentage / 100)
            stop_loss_price = current_price * (1 - sl_percentage / 100)
        else:  # SHORT
            take_profit_price = current_price * (1 - tp_percentage / 100)
            stop_loss_price = current_price * (1 + sl_percentage / 100)

        # Open position in trade manager
        trade_position = self.trade_manager.open_position(
            symbol=symbol,
            side=side,
            entry_price=current_price,
            position_size=position_size,
            leverage=leverage,
            take_profit_price=take_profit_price,
            stop_loss_price=stop_loss_price,
            model_confidence=confidence,
            signal_score=0.0  # Placeholder
        )

        # Also open position in account for balance tracking
        account_side = 'buy' if side == 'LONG' else 'sell'
        position = self.account.open_position(symbol, account_side, position_size, current_price, leverage)

        if position and trade_position:
            self.log_message.emit({
                'level': 'info',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Opened {side} position for {position_size} {symbol} at {current_price} with {leverage}x leverage"
            })
            self.log_message.emit({
                'level': 'info',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Take profit: {take_profit_price:.6f}, Stop loss: {stop_loss_price:.6f}"
            })

            # Update active positions in current state
            self.update_positions()

            # Emit updated state
            self.data_updated.emit(self.current_state)

    def periodic_decision(self):
        """Make trading decision periodically"""
        if self.trading_active and self.trade_manager:
            self.make_trading_decision()

    def update_positions(self):
        """Update positions with current prices"""
        if not self.trading_active or not self.trade_manager:
            return

        # Get symbol
        symbol = self.parameters.get('symbol', 'DOGE/USDT')

        # Get current price from data manager
        features = self.data_manager.get_data(symbol, 'features')
        current_price = features.get('spot_close', 0) or features.get('futures_close', 0)

        if not current_price:
            return

        # Update positions in trade manager
        self.trade_manager.update_positions(current_price, symbol)

        # Check for positions that should be closed based on TP/SL
        positions_to_close = self.trade_manager.check_exit_conditions(current_price, symbol)
        for position in positions_to_close:
            closed_position = self.trade_manager.close_position(position['id'], current_price, position['exit_reason'])

            if closed_position:
                # Also close position in account
                for pos_id, pos in self.account.get_open_positions().items():
                    if pos['symbol'] == symbol:
                        self.account.close_position(pos_id, current_price)
                        break

                # Log position closure
                pnl = closed_position['realized_pnl']
                pnl_pct = closed_position['pnl_percentage']
                reason = closed_position['exit_reason'].upper()

                self.log_message.emit({
                    'level': 'info',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'message': f"Closed {closed_position['side']} position at {closed_position['exit_price']:.6f}, PnL: {pnl:.2f} ({pnl_pct:.2f}%), Reason: {reason}"
                })

        # Update active positions in current state
        if self.use_real_exchange:
            # Fetch open positions from the exchange
            try:
                print("\n\n*** FETCHING OPEN POSITIONS FROM EXCHANGE IN UPDATE_POSITIONS METHOD ***\n\n")
                open_positions = self.exchange.fetch_open_positions(symbol)
                print(f"\n\n*** FETCHED {len(open_positions)} OPEN POSITIONS FROM EXCHANGE ***\n\n")

                # Print the positions for debugging
                for i, pos in enumerate(open_positions):
                    print(f"\n\n*** POSITION {i+1} IN UPDATE_POSITIONS: ***")
                    for key, value in pos.items():
                        print(f"{key}: {value}")
                    print("\n")

                self.current_state['active_positions'] = open_positions
                print(f"\n\n*** UPDATED CURRENT STATE WITH {len(open_positions)} ACTIVE POSITIONS ***\n\n")
                logger.info(f"Fetched {len(open_positions)} open positions from exchange")
            except Exception as e:
                print(f"\n\n*** ERROR FETCHING OPEN POSITIONS FROM EXCHANGE: {e} ***\n\n")
                logger.error(f"Error fetching open positions from exchange: {e}")
                # Fall back to simulated positions
                open_positions = self.trade_manager.get_open_positions(symbol)
                self.current_state['active_positions'] = open_positions
        else:
            # Use simulated positions
            open_positions = self.trade_manager.get_open_positions(symbol)
            self.current_state['active_positions'] = open_positions

        # Update account balance
        self.current_state['account_balance'] = self.account.get_balance()

        # Update position history
        closed_positions = self.trade_manager.get_closed_positions(symbol)
        self.current_state['position_history'] = closed_positions

        # Emit updated state
        self.data_updated.emit(self.current_state)

    def refresh_data(self):
        """Manually refresh data"""
        # Run data update in background thread
        worker = DataUpdateWorker(self.parameters, self.data_manager)
        worker.trading_active = self.trading_active  # Pass trading_active status
        worker.signals.result.connect(self.handle_data_update)
        worker.signals.error.connect(self.handle_error)

        # Execute
        self.threadpool.start(worker)

        # Log refresh
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Manually refreshing data for {self.parameters['symbol']}"
        })

    def update_data(self):
        """Update data periodically"""
        # Only update if trading is active
        if not self.trading_active:
            return

        # Run data update in background thread
        worker = DataUpdateWorker(self.parameters, self.data_manager)
        worker.trading_active = self.trading_active  # Pass trading_active status
        worker.signals.result.connect(self.handle_data_update)
        worker.signals.error.connect(self.handle_error)

        # Execute
        self.threadpool.start(worker)

    def on_data_updated(self, update_info):
        """Handle data updates from shared data manager"""
        # Only process updates for our symbol
        if update_info.get('symbol') != self.parameters.get('symbol', 'DOGE/USDT'):
            return

        # If we're actively trading, trigger a data update
        if self.trading_active:
            self.update_data()

    def handle_data_update(self, result):
        """Handle data update result"""
        # Update current state
        for key, value in result.items():
            self.current_state[key] = value

        # Update last update time
        self.current_state['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Update positions if trading is active
        if self.trading_active and self.trade_manager:
            self.update_positions()

        # Emit data updated signal
        self.data_updated.emit(self.current_state)

    def handle_error(self, error):
        """Handle error"""
        # Log error
        self.log_message.emit({
            'level': 'error',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Error: {error}"
        })

    def run_backtest(self, params):
        """Run backtest"""
        # Run backtest in background thread
        worker = BacktestWorker(params)
        worker.signals.result.connect(self.handle_backtest_result)
        worker.signals.progress.connect(self.handle_backtest_progress)
        worker.signals.error.connect(self.handle_error)

        # Execute
        self.threadpool.start(worker)

        # Log backtest start
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Starting backtest for {params['symbol']} from {params['start_date']} to {params['end_date']}"
        })

    def handle_backtest_result(self, result):
        """Handle backtest result"""
        # Update performance data
        self.current_state['performance_data'] = result

        # Emit data updated signal
        self.data_updated.emit(self.current_state)

        # Log backtest completion
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Backtest completed: {result['summary']}"
        })

    def handle_backtest_progress(self, progress):
        """Handle backtest progress"""
        # Log progress
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Backtest progress: {progress}%"
        })

    def export_results(self, export_params):
        """Export trading results"""
        # Export results in background thread
        worker = ExportWorker(export_params, self.current_state)
        worker.signals.result.connect(self.handle_export_result)
        worker.signals.error.connect(self.handle_error)

        # Execute
        self.threadpool.start(worker)

        # Log export start
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Exporting results to {export_params['format']} format"
        })

    def handle_export_result(self, result):
        """Handle export result"""
        # Log export completion
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Export completed: {result['file_path']}"
        })

    def close_position(self, position_id):
        """Close a position"""
        # Find position
        position = next((p for p in self.current_state['active_positions'] if p.get('id') == position_id), None)

        if not position:
            # Log error
            self.log_message.emit({
                'level': 'error',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': f"Position not found: {position_id}"
            })
            return

        # Close position in background thread
        worker = ClosePositionWorker(position)
        worker.signals.result.connect(self.handle_close_position_result)
        worker.signals.error.connect(self.handle_error)

        # Execute
        self.threadpool.start(worker)

        # Log close position request
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Closing position: {position['symbol']} {position['side']} {position['size']}"
        })

    def handle_close_position_result(self, result):
        """Handle close position result"""
        # Update position history
        self.current_state['position_history'].append(result)

        # Remove from active positions
        self.current_state['active_positions'] = [p for p in self.current_state['active_positions'] if p.get('id') != result.get('id')]

        # Emit data updated signal
        self.data_updated.emit(self.current_state)

        # Log position closed
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': f"Position closed: {result['symbol']} {result['side']} with PnL: ${result['realized_pnl']:.2f} ({result['realized_pnl_pct']:.2f}%)"
        })

    def get_current_state(self):
        """Get current state"""
        return self.current_state

    def cleanup(self):
        """Clean up resources"""
        # Stop trading
        self.stop_trading()

        # Stop update timer
        self.update_timer.stop()

        # Stop decision timer if it exists
        if hasattr(self, 'decision_timer'):
            self.decision_timer.stop()

        # Save parameters
        self.save_parameters()

        # Close any open positions
        if self.trade_manager:
            # Get symbol
            symbol = self.parameters.get('symbol', 'DOGE/USDT')

            # Get current price from data manager
            features = self.data_manager.get_data(symbol, 'features')
            current_price = features.get('spot_close', 0) or features.get('futures_close', 0)

            if current_price > 0:
                # Close positions in trade manager
                open_positions = self.trade_manager.get_open_positions(symbol)
                for position in open_positions:
                    self.trade_manager.close_position(position['id'], current_price, 'manual_close')

                # Close positions in account
                for pos_id, pos in self.account.get_open_positions().items():
                    if pos['symbol'] == symbol:
                        self.account.close_position(pos_id, current_price)

        # Log cleanup
        self.log_message.emit({
            'level': 'info',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': "Trading system resources cleaned up"
        })


# Worker classes for background tasks

class WorkerSignals(QObject):
    """Signals for worker threads"""
    result = pyqtSignal(dict)
    error = pyqtSignal(str)
    progress = pyqtSignal(int)


class DataUpdateWorker(QRunnable):
    """Worker for updating data in background"""

    def __init__(self, parameters, data_manager):
        super().__init__()
        self.parameters = parameters
        self.data_manager = data_manager
        self.signals = WorkerSignals()
        self.trading_active = False  # Default to False

    def run(self):
        """Run the worker"""
        try:
            # Get symbol from parameters
            symbol = self.parameters.get('symbol', 'DOGE/USDT')

            # Get data from shared data manager
            combined_data = self.data_manager.get_data(symbol, 'combined_data')
            features = self.data_manager.get_data(symbol, 'features')

            # Process data
            if combined_data and features:
                result = self.process_data(symbol, combined_data, features)
            else:
                # Try to get real data directly
                result = self.get_real_data()
                if not result:
                    # No data available
                    self.signals.error.emit(f"No data available for {symbol}")
                    return

            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(str(e))

    def process_data(self, symbol, combined_data, features):
        """Process data from shared data manager"""
        # Get current price
        current_price = features.get('spot_close', 0) or features.get('futures_close', 0)
        if not current_price and combined_data.get('spot_orderbook', {}).get('mid_price'):
            current_price = combined_data['spot_orderbook']['mid_price']

        # Initialize result dictionary
        result = {}

        # Set connection status
        result['connection_status'] = 'Connected'

        # Add symbol to result
        result['symbol'] = symbol

        # Generate signal data
        signal_data = self.generate_signal_data(features)
        result['signal_data'] = signal_data

        # Generate performance data
        performance_data = self.generate_performance_data(current_price)
        result['performance_data'] = performance_data

        # Generate regime data
        regime_data = self.generate_regime_data(features)
        result['regime_data'] = regime_data

        # Generate timeframe data
        timeframe_data = self.generate_timeframe_data(combined_data)
        result['timeframe_data'] = timeframe_data

        # Generate account data
        result['account_balance'] = 500.0  # Use simulated account balance

        # Generate active positions and position history
        result['active_positions'] = []  # No active positions for now
        result['position_history'] = []  # No position history for now

        # Add current price to result
        result['current_price'] = current_price

        return result

    def generate_signal_data(self, features):
        """Generate signal data from features"""
        # Extract relevant features
        signal_data = {'signals': {}}

        # Volume imbalance signal
        if 'spot_volume_imbalance' in features:
            volume_imbalance = features['spot_volume_imbalance']
            signal_data['signals']['volume_imbalance'] = {
                'value': volume_imbalance,
                'strength': abs(volume_imbalance) * 10  # Scale to 0-10
            }

        # Order book signal
        if 'futures_volume_imbalance' in features:
            orderbook_signal = features['futures_volume_imbalance']
            signal_data['signals']['orderbook'] = {
                'value': orderbook_signal,
                'strength': abs(orderbook_signal) * 10  # Scale to 0-10
            }

        # Futures premium signal
        if 'futures_premium_pct' in features:
            premium = features['futures_premium_pct']
            signal_data['signals']['futures_premium'] = {
                'value': premium,
                'strength': abs(premium) * 2  # Scale to 0-10
            }

        # Trade frequency signal
        if 'futures_trade_frequency' in features:
            trade_freq = min(features['futures_trade_frequency'] / 10, 1.0)  # Normalize to 0-1
            signal_data['signals']['trade_frequency'] = {
                'value': trade_freq,
                'strength': trade_freq * 10  # Scale to 0-10
            }

        # Buy/sell ratio signal
        if 'futures_buy_ratio' in features:
            buy_ratio = features['futures_buy_ratio']
            # Convert from 0-1 to -1 to 1 (0.5 is neutral)
            buy_sell_signal = (buy_ratio - 0.5) * 2
            signal_data['signals']['buy_sell_ratio'] = {
                'value': buy_sell_signal,
                'strength': abs(buy_sell_signal) * 10  # Scale to 0-10
            }

        # Calculate total score (weighted average of signals)
        total_score = 0
        total_weight = 0

        weights = {
            'volume_imbalance': 0.2,
            'orderbook': 0.3,
            'futures_premium': 0.2,
            'trade_frequency': 0.1,
            'buy_sell_ratio': 0.2
        }

        for signal, data in signal_data['signals'].items():
            weight = weights.get(signal, 0.1)
            total_score += data['value'] * weight
            total_weight += weight

        if total_weight > 0:
            signal_data['signals']['total_score'] = total_score / total_weight
        else:
            signal_data['signals']['total_score'] = 0

        return signal_data

    def generate_performance_data(self, current_price):
        """Generate performance data using current price"""
        # Use current time
        now = datetime.now()

        # Generate equity and price data points
        equity_data = []
        price_data = []
        trade_markers = []

        # Get symbol
        symbol = self.parameters.get('symbol', 'DOGE/USDT') if hasattr(self, 'parameters') else 'DOGE/USDT'

        # If we have historical data in the account, use it
        if hasattr(self, 'account') and hasattr(self.account, 'balance_history') and self.account.balance_history:
            # Convert account balance history to equity data
            for timestamp, balance in self.account.balance_history.items():
                equity_data.append({
                    'timestamp': timestamp,
                    'value': balance
                })

            # Sort by timestamp
            equity_data = sorted(equity_data, key=lambda x: x['timestamp'])

        # If we don't have enough equity data points, generate some
        if len(equity_data) < 24:
            # Generate data for the last 24 hours
            for i in range(24):
                timestamp = now - timedelta(hours=24-i)
                unix_timestamp = timestamp.timestamp()

                # Generate equity based on price
                equity = 500 + (i/24) * 10  # Simple upward trend

                # Add data points
                equity_data.append({
                    'timestamp': unix_timestamp,
                    'value': equity
                })

        # Get price history from data manager if available
        price_history = None
        if hasattr(self, 'data_manager'):
            price_history = self.data_manager.get_data(symbol, 'price_history')

        if price_history:
            # Use real price history
            price_data = price_history
        else:
            # Generate price data for the last 24 hours
            for i in range(24):
                timestamp = now - timedelta(hours=24-i)
                unix_timestamp = timestamp.timestamp()

                # Generate price with some randomness around current price
                price_factor = 1 + (i - 12) * 0.001  # Slight trend
                price = current_price * price_factor * (1 + random.uniform(-0.01, 0.01))

                # Add data points
                price_data.append({
                    'timestamp': unix_timestamp,
                    'value': price
                })

        # Get trade history if available
        if hasattr(self, 'trade_manager') and self.trade_manager:
            # Get closed positions
            symbol = self.parameters.get('symbol', 'DOGE/USDT') if hasattr(self, 'parameters') else 'DOGE/USDT'
            closed_positions = self.trade_manager.get_closed_positions(symbol)

            # Add trade markers for each position
            for position in closed_positions:
                # Entry marker
                entry_timestamp = datetime.strptime(position['entry_time'], '%Y-%m-%d %H:%M:%S').timestamp()

                trade_markers.append({
                    'timestamp': entry_timestamp,
                    'price': position['entry_price'],
                    'type': 'entry',
                    'direction': position['side'].lower()
                })

                # Exit marker
                exit_timestamp = datetime.strptime(position['exit_time'], '%Y-%m-%d %H:%M:%S').timestamp()

                trade_markers.append({
                    'timestamp': exit_timestamp,
                    'price': position['exit_price'],
                    'type': 'exit',
                    'direction': position['side'].lower(),
                    'pnl': f"{position['pnl_percentage']:.2f}%"
                })

            # Add markers for open positions
            open_positions = self.trade_manager.get_open_positions(symbol)

            for position in open_positions:
                # Entry marker
                entry_timestamp = datetime.strptime(position['entry_time'], '%Y-%m-%d %H:%M:%S').timestamp()

                trade_markers.append({
                    'timestamp': entry_timestamp,
                    'price': position['entry_price'],
                    'type': 'entry',
                    'direction': position['side'].lower()
                })
        else:
            # Generate a few mock trade markers
            for i in range(3):
                # Entry marker
                timestamp = now - timedelta(hours=random.randint(1, 20))
                unix_timestamp = timestamp.timestamp()

                direction = random.choice(['long', 'short'])
                price = current_price * (1 + random.uniform(-0.05, 0.05))

                trade_markers.append({
                    'timestamp': unix_timestamp,
                    'price': price,
                    'type': 'entry',
                    'direction': direction
                })

                # Exit marker (a bit later)
                exit_timestamp = timestamp + timedelta(minutes=random.randint(5, 120))
                unix_exit_timestamp = exit_timestamp.timestamp()
                exit_price = price * (1 + random.uniform(-0.03, 0.03))

                # Calculate PnL
                if direction == 'long':
                    pnl = f"+{(exit_price / price - 1) * 100:.2f}%" if exit_price > price else f"{(exit_price / price - 1) * 100:.2f}%"
                else:
                    pnl = f"+{(price / exit_price - 1) * 100:.2f}%" if exit_price < price else f"{(price / exit_price - 1) * 100:.2f}%"

                trade_markers.append({
                    'timestamp': unix_exit_timestamp,
                    'price': exit_price,
                    'type': 'exit',
                    'direction': direction,
                    'pnl': pnl
                })

        performance_data = {
            'equity_data': equity_data,
            'price_data': price_data,
            'trade_markers': trade_markers
        }

        return performance_data

    def generate_regime_data(self, features):
        """Generate market regime data from features"""
        # Define market regime types
        regime_types = ['high_volatility', 'low_volatility', 'range_bound', 'strong_trend', 'weak_trend', 'uncertain']

        # Determine primary regime based on features
        if 'futures_trade_frequency' in features and features['futures_trade_frequency'] > 5:
            primary_regime = 'high_volatility'
        elif 'spot_volume_imbalance' in features and abs(features['spot_volume_imbalance']) > 0.7:
            primary_regime = 'strong_trend'
        elif 'futures_buy_ratio' in features and (features['futures_buy_ratio'] > 0.7 or features['futures_buy_ratio'] < 0.3):
            primary_regime = 'strong_trend'
        elif 'futures_premium_pct' in features and abs(features['futures_premium_pct']) < 0.1:
            primary_regime = 'range_bound'
        else:
            primary_regime = 'uncertain'

        # Generate regime scores
        regime_scores = {}
        total_score = 0

        for regime in regime_types:
            if regime == primary_regime:
                score = random.uniform(0.6, 0.9)
            else:
                score = random.uniform(0.0, 0.3)
            regime_scores[regime] = score
            total_score += score

        # Normalize scores
        for regime in regime_types:
            regime_scores[regime] /= total_score

        # Generate regime metrics
        trend_strength = random.uniform(-0.8, 0.8)
        volatility = random.uniform(0.2, 3.0)

        # Adjust based on features if available
        if 'futures_buy_ratio' in features:
            # Convert from 0-1 to -1 to 1 (0.5 is neutral)
            trend_strength = (features['futures_buy_ratio'] - 0.5) * 2

        if 'futures_trade_frequency' in features:
            volatility = min(features['futures_trade_frequency'] / 5, 3.0)

        # Calculate regime alignment
        alignment = random.uniform(0, 1)

        # Create regime data
        regime_data = {
            'primary_regime': primary_regime,
            'regime_scores': regime_scores,
            'metrics': {
                'trend_strength': trend_strength,
                'volatility': volatility,
                'alignment': alignment
            }
        }

        return regime_data

    def generate_timeframe_data(self, combined_data):
        """Generate multi-timeframe analysis data"""
        # Define timeframes
        timeframes = ['1m', '5m', '15m', '1h', '4h']

        # Generate timeframe analysis
        timeframe_analysis = {}

        for tf in timeframes:
            # Generate random trend direction
            trend_direction = random.choice(['bullish', 'bearish', 'neutral'])

            # Generate random strength (0-1)
            strength = random.uniform(0, 1)

            # Generate random volatility (0-1)
            volatility = random.uniform(0, 1)

            # Generate random signal quality (0-1)
            signal_quality = random.uniform(0, 1)

            timeframe_analysis[tf] = {
                'trend': trend_direction,
                'strength': strength,
                'volatility': volatility,
                'signal_quality': signal_quality
            }

        # Try to use real data if available
        if 'spot_ohlcv' in combined_data and combined_data['spot_ohlcv']:
            # Use the last candle's direction for 1m timeframe
            ohlcv = combined_data['spot_ohlcv'][-1]
            if 'open' in ohlcv and 'close' in ohlcv:
                if ohlcv['close'] > ohlcv['open']:
                    timeframe_analysis['1m']['trend'] = 'bullish'
                    timeframe_analysis['1m']['strength'] = min((ohlcv['close'] - ohlcv['open']) / ohlcv['open'] * 100, 1.0)
                elif ohlcv['close'] < ohlcv['open']:
                    timeframe_analysis['1m']['trend'] = 'bearish'
                    timeframe_analysis['1m']['strength'] = min((ohlcv['open'] - ohlcv['close']) / ohlcv['open'] * 100, 1.0)
                else:
                    timeframe_analysis['1m']['trend'] = 'neutral'
                    timeframe_analysis['1m']['strength'] = 0.0

        # Calculate trend alignment
        bullish_count = sum(1 for _, data in timeframe_analysis.items() if data['trend'] == 'bullish')
        bearish_count = sum(1 for _, data in timeframe_analysis.items() if data['trend'] == 'bearish')

        if bullish_count > bearish_count:
            trend_direction = 'bullish'
            trend_alignment = bullish_count / len(timeframes)
        elif bearish_count > bullish_count:
            trend_direction = 'bearish'
            trend_alignment = bearish_count / len(timeframes)
        else:
            trend_direction = 'neutral'
            trend_alignment = 0.5

        # Calculate average signal quality
        signal_quality = sum(data['signal_quality'] for data in timeframe_analysis.values()) / len(timeframes)

        # Generate timeframe correlation
        timeframe_correlation = {}
        for i, tf1 in enumerate(timeframes):
            for j, tf2 in enumerate(timeframes):
                if i == j:
                    correlation = 1.0
                else:
                    # Generate random correlation, higher for adjacent timeframes
                    if abs(i - j) == 1:
                        correlation = random.uniform(0.5, 0.9)
                    else:
                        correlation = random.uniform(0.2, 0.6)

                timeframe_correlation[f"{tf1}_{tf2}"] = correlation

        timeframe_data = {
            'timeframe_analysis': timeframe_analysis,
            'trend_metrics': {
                'trend_strength': sum(data['strength'] for data in timeframe_analysis.values()) / len(timeframes),
                'trend_direction': trend_direction,
                'trend_alignment': trend_alignment,
                'signal_quality': signal_quality,
                'timeframes_available': len(timeframes),
                'volatility': sum(data['volatility'] for data in timeframe_analysis.values()) / len(timeframes)
            },
            'timeframe_correlation': timeframe_correlation
        }

        return timeframe_data

    # Mock data generation has been removed in favor of real data
    def get_real_data(self):
        """Get real data from the trading system"""
        # Initialize result dictionary
        result = {
            'connection_status': 'Connected' if self.trading_active else 'Disconnected',
            'account_balance': 0,
            'active_positions': [],
            'position_history': [],
            'signal_data': {'signals': {}},
            'performance_data': {},
            'regime_data': {},
            'timeframe_data': {}
        }

        try:
            # Get account data if available
            if hasattr(self, 'account') and self.account:
                # Get account balance
                result['account_balance'] = self.account.get_balance()

                # Get active positions
                result['active_positions'] = self.account.get_open_positions()

                # Get position history
                result['position_history'] = self.account.get_closed_positions()

            # Get signal data if available
            if hasattr(self, 'signal_scorer') and self.signal_scorer:
                # Get latest signals
                result['signal_data'] = self.signal_scorer.get_latest_signals()

            # Get performance data
            symbol = self.parameters.get('symbol', 'DOGE/USDT')
            current_price = 0

            # Try to get current price from data manager
            if hasattr(self, 'data_manager') and self.data_manager:
                combined_data = self.data_manager.get_data(symbol, 'combined_data')
                if combined_data and 'spot_ohlcv' in combined_data and combined_data['spot_ohlcv']:
                    # Get the latest price
                    current_price = combined_data['spot_ohlcv'][-1]['close']

            # Generate performance data using current price
            result['performance_data'] = self.generate_performance_data(current_price)

            # Get regime data if available
            if hasattr(self, 'regime_classifier') and self.regime_classifier:
                result['regime_data'] = self.regime_classifier.get_current_regime()

            # Get timeframe data
            if hasattr(self, 'data_manager') and self.data_manager:
                combined_data = self.data_manager.get_data(symbol, 'combined_data')
                if combined_data:
                    result['timeframe_data'] = self.generate_timeframe_data(combined_data)
        except Exception as e:
            print(f"Error getting real data: {e}")

        return result

    def generate_performance_data(self, current_price):
        """Generate performance data using current price"""
        # Check if we already have cached data and it's recent enough
        if hasattr(self, '_cached_performance_data') and hasattr(self, '_last_performance_update'):
            # Only regenerate data every 5 seconds to reduce lag
            if (datetime.now() - self._last_performance_update).total_seconds() < 5:
                return self._cached_performance_data

        # Use current time
        now = datetime.now()

        # Initialize data structures
        equity_data = []
        price_data = []
        trade_markers = []

        # Try to use real data from the account if available
        if hasattr(self, 'account') and self.account:
            # Get balance history from account
            balance_history = self.account.get_balance_history()

            # Convert balance history to equity data
            if balance_history:
                for timestamp, balance in balance_history.items():
                    try:
                        # Convert to the format expected by the chart panel
                        equity_data.append({'timestamp': float(timestamp), 'value': balance})
                    except (ValueError, TypeError):
                        pass  # Silently skip invalid entries

                # Sort by timestamp
                equity_data.sort(key=lambda x: x['timestamp'])

            # Get trade history for trade markers
            trade_history = self.account.get_trade_history()

            # If we have trade history, use it for trade markers
            if trade_history:
                for trade in trade_history:
                    try:
                        if trade['type'] == 'open':
                            # Entry marker
                            trade_markers.append({
                                'timestamp': trade['timestamp'].timestamp() * 1000,  # Convert to milliseconds for JS
                                'price': trade['price'],
                                'type': 'entry',
                                'direction': 'long' if trade['side'] == 'buy' else 'short'
                            })
                        elif trade['type'] == 'close':
                            # Exit marker
                            trade_markers.append({
                                'timestamp': trade['timestamp'].timestamp() * 1000,  # Convert to milliseconds for JS
                                'price': trade['price'],
                                'type': 'exit',
                                'direction': 'long' if trade['side'] == 'sell' else 'short',
                                'pnl': f"{trade.get('pnl', 0):.2f}"
                            })
                    except Exception:
                        pass  # Silently skip invalid entries

        # If we don't have real data, create a minimal dataset
        if not equity_data:
            # Create a single data point with the current time and balance
            timestamp = now.timestamp()
            balance = 1000.0  # Default starting balance

            if hasattr(self, 'account') and self.account:
                balance = self.account.get_balance()

            equity_data.append({'timestamp': timestamp, 'value': balance})

        # Try to get price history from data manager
        if not price_data and hasattr(self, 'data_manager') and self.data_manager:
            # Get symbol
            symbol = self.parameters.get('symbol', 'DOGE/USDT') if hasattr(self, 'parameters') else 'DOGE/USDT'

            # Get combined data from data manager
            combined_data = self.data_manager.get_data(symbol, 'combined_data')

            # If we have OHLCV data, use it for price data
            if combined_data and 'spot_ohlcv' in combined_data and combined_data['spot_ohlcv']:
                ohlcv_data = combined_data['spot_ohlcv']

                # Convert OHLCV data to price data
                for candle in ohlcv_data:
                    if 'timestamp' in candle and 'close' in candle:
                        # Use the format expected by the chart panel
                        price_data.append({'timestamp': candle['timestamp'], 'value': candle['close']})

                # Sort by timestamp
                price_data.sort(key=lambda x: x['timestamp'])

        # If we don't have real price data, create a minimal dataset
        if not price_data:
            # Create a single data point with the current time and price
            timestamp = now.timestamp()
            price_data.append({'timestamp': timestamp, 'value': current_price})

        # We don't generate mock trade markers anymore
        # If there are no real trade markers, that's fine

        performance_data = {
            'equity_data': equity_data,
            'price_data': price_data,
            'trade_markers': trade_markers
        }

        # Cache the data and update timestamp
        self._cached_performance_data = performance_data
        self._last_performance_update = datetime.now()

        return performance_data

    def generate_regime_data(self, features):
        """Generate market regime data from features"""
        # Define market regime types
        regime_types = ['high_volatility', 'low_volatility', 'range_bound', 'strong_trend', 'weak_trend', 'uncertain']

        # Determine primary regime based on features
        if 'futures_trade_frequency' in features and features['futures_trade_frequency'] > 5:
            primary_regime = 'high_volatility'
        elif 'spot_volume_imbalance' in features and abs(features['spot_volume_imbalance']) > 0.7:
            primary_regime = 'strong_trend'
        elif 'futures_buy_ratio' in features and (features['futures_buy_ratio'] > 0.7 or features['futures_buy_ratio'] < 0.3):
            primary_regime = 'strong_trend'
        elif 'futures_premium_pct' in features and abs(features['futures_premium_pct']) < 0.1:
            primary_regime = 'range_bound'
        else:
            primary_regime = 'uncertain'

        # Generate regime scores
        regime_scores = {}
        total_score = 0

        for regime in regime_types:
            if regime == primary_regime:
                score = random.uniform(0.6, 0.9)
            else:
                score = random.uniform(0.0, 0.3)
            regime_scores[regime] = score
            total_score += score

        # Normalize scores
        for regime in regime_types:
            regime_scores[regime] /= total_score

        # Generate regime metrics
        trend_strength = random.uniform(-0.8, 0.8)
        volatility = random.uniform(0.2, 3.0)

        # Adjust based on features if available
        if 'futures_buy_ratio' in features:
            # Convert from 0-1 to -1 to 1 (0.5 is neutral)
            trend_strength = (features['futures_buy_ratio'] - 0.5) * 2

        if 'futures_trade_frequency' in features:
            volatility = min(features['futures_trade_frequency'] / 5, 3.0)

        # Calculate regime alignment
        alignment = random.uniform(0, 1)

        # Create regime data
        regime_data = {
            'primary_regime': primary_regime,
            'regime_scores': regime_scores,
            'metrics': {
                'trend_strength': trend_strength,
                'volatility': volatility,
                'alignment': alignment
            }
        }

        return regime_data

    def generate_timeframe_data(self, combined_data):
        """Generate multi-timeframe analysis data"""
        # Define timeframes
        timeframes = ['1m', '5m', '15m', '1h', '4h']

        # Generate timeframe analysis
        timeframe_analysis = {}

        for tf in timeframes:
            # Generate random trend direction
            trend_direction = random.choice(['bullish', 'bearish', 'neutral'])

            # Generate random strength (0-1)
            strength = random.uniform(0, 1)

            # Generate random volatility (0-1)
            volatility = random.uniform(0, 1)

            # Generate random signal quality (0-1)
            signal_quality = random.uniform(0, 1)

            timeframe_analysis[tf] = {
                'trend': trend_direction,
                'strength': strength,
                'volatility': volatility,
                'signal_quality': signal_quality
            }

        # Try to use real data if available
        if 'spot_ohlcv' in combined_data and combined_data['spot_ohlcv']:
            # Use the last candle's direction for 1m timeframe
            ohlcv = combined_data['spot_ohlcv'][-1]
            if 'open' in ohlcv and 'close' in ohlcv:
                if ohlcv['close'] > ohlcv['open']:
                    timeframe_analysis['1m']['trend'] = 'bullish'
                    timeframe_analysis['1m']['strength'] = min((ohlcv['close'] - ohlcv['open']) / ohlcv['open'] * 100, 1.0)
                elif ohlcv['close'] < ohlcv['open']:
                    timeframe_analysis['1m']['trend'] = 'bearish'
                    timeframe_analysis['1m']['strength'] = min((ohlcv['open'] - ohlcv['close']) / ohlcv['open'] * 100, 1.0)
                else:
                    timeframe_analysis['1m']['trend'] = 'neutral'
                    timeframe_analysis['1m']['strength'] = 0.0

        # Calculate trend alignment
        bullish_count = sum(1 for _, data in timeframe_analysis.items() if data['trend'] == 'bullish')
        bearish_count = sum(1 for _, data in timeframe_analysis.items() if data['trend'] == 'bearish')

        if bullish_count > bearish_count:
            trend_direction = 'bullish'
            trend_alignment = bullish_count / len(timeframes)
        elif bearish_count > bullish_count:
            trend_direction = 'bearish'
            trend_alignment = bearish_count / len(timeframes)
        else:
            trend_direction = 'neutral'
            trend_alignment = 0.5

        # Calculate average signal quality
        signal_quality = sum(data['signal_quality'] for data in timeframe_analysis.values()) / len(timeframes)

        # Generate timeframe correlation
        timeframe_correlation = {}
        for i, tf1 in enumerate(timeframes):
            for j, tf2 in enumerate(timeframes):
                if i == j:
                    correlation = 1.0
                else:
                    # Generate random correlation, higher for adjacent timeframes
                    if abs(i - j) == 1:
                        correlation = random.uniform(0.5, 0.9)
                    else:
                        correlation = random.uniform(0.2, 0.6)

                timeframe_correlation[f"{tf1}_{tf2}"] = correlation

        timeframe_data = {
            'timeframe_analysis': timeframe_analysis,
            'trend_metrics': {
                'trend_strength': sum(data['strength'] for data in timeframe_analysis.values()) / len(timeframes),
                'trend_direction': trend_direction,
                'trend_alignment': trend_alignment,
                'signal_quality': signal_quality,
                'timeframes_available': len(timeframes),
                'volatility': sum(data['volatility'] for data in timeframe_analysis.values()) / len(timeframes)
            },
            'timeframe_correlation': timeframe_correlation
        }

        return timeframe_data

    def generate_mock_data(self):
        """Generate mock data for testing"""
        import random
        from datetime import datetime, timedelta

        # Current time
        now = datetime.now()

        # Generate mock account data
        account_balance = 500 + random.uniform(-50, 50)

        # Generate mock active positions
        active_positions = []
        if random.random() > 0.7:  # 30% chance of having a position
            position_id = f"pos_{int(time.time())}"
            symbol = self.parameters['symbol']
            side = random.choice(['LONG', 'SHORT'])
            size = random.uniform(0.1, 1.0)
            entry_price = random.uniform(0.15, 0.16)
            current_price = entry_price * (1 + random.uniform(-0.02, 0.02))

            # Calculate PnL
            if side == 'LONG':
                unrealized_pnl = (current_price - entry_price) * size
                unrealized_pnl_pct = (current_price / entry_price - 1) * 100
            else:
                unrealized_pnl = (entry_price - current_price) * size
                unrealized_pnl_pct = (entry_price / current_price - 1) * 100

            # Take profit and stop loss
            take_profit = entry_price * (1 + self.parameters['take_profit'] / 100) if side == 'LONG' else entry_price * (1 - self.parameters['take_profit'] / 100)
            stop_loss = entry_price * (1 - self.parameters['stop_loss'] / 100) if side == 'LONG' else entry_price * (1 + self.parameters['stop_loss'] / 100)

            active_positions.append({
                'id': position_id,
                'symbol': symbol,
                'side': side,
                'size': size,
                'entry_price': entry_price,
                'current_price': current_price,
                'unrealized_pnl': unrealized_pnl,
                'unrealized_pnl_pct': unrealized_pnl_pct,
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'entry_time': (now - timedelta(minutes=random.randint(5, 60))).strftime('%Y-%m-%d %H:%M:%S')
            })

        # Generate mock position history
        position_history = []
        for i in range(random.randint(3, 8)):
            symbol = self.parameters['symbol']
            side = random.choice(['LONG', 'SHORT'])
            size = random.uniform(0.1, 1.0)
            entry_price = random.uniform(0.15, 0.16)
            exit_price = entry_price * (1 + random.uniform(-0.05, 0.05))

            # Calculate PnL
            if side == 'LONG':
                realized_pnl = (exit_price - entry_price) * size
                realized_pnl_pct = (exit_price / entry_price - 1) * 100
            else:
                realized_pnl = (entry_price - exit_price) * size
                realized_pnl_pct = (entry_price / exit_price - 1) * 100

            # Entry and exit times
            entry_time = now - timedelta(hours=random.randint(1, 24))
            exit_time = entry_time + timedelta(minutes=random.randint(5, 120))
            duration = str(exit_time - entry_time).split('.')[0]  # Format as HH:MM:SS

            position_history.append({
                'id': f"hist_{i}",
                'symbol': symbol,
                'side': side,
                'size': size,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'realized_pnl': realized_pnl,
                'realized_pnl_pct': realized_pnl_pct,
                'entry_time': entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                'exit_time': exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': duration
            })

        # Generate mock signal data
        macd_score = random.uniform(-0.5, 0.5)
        ob_score = random.uniform(-0.3, 0.3)
        vol_score = random.uniform(-0.4, 0.4)
        pa_score = random.uniform(-0.2, 0.2)
        trend_score = random.uniform(-0.6, 0.6)

        total_score = (
            macd_score * self.parameters['signal_weights']['macd'] +
            ob_score * self.parameters['signal_weights']['orderbook'] +
            vol_score * self.parameters['signal_weights']['volume'] +
            pa_score * self.parameters['signal_weights']['price_action']
        )

        confidence = 50 + total_score * 100  # Scale to 0-100 range
        confidence = max(0, min(100, confidence))  # Clamp to 0-100

        alignment = 50 + random.uniform(-20, 20)

        # Determine decision
        if total_score > 0.1 and confidence > self.parameters['min_confidence']:
            decision = 'LONG'
        elif total_score < -0.1 and confidence > self.parameters['min_confidence']:
            decision = 'SHORT'
        else:
            decision = 'WAIT'

        signal_data = {
            'signals': {
                'macd_score': macd_score,
                'orderbook_score': ob_score,
                'volume_score': vol_score,
                'price_action_score': pa_score,
                'trend_score': trend_score,
                'total_score': total_score
            },
            'confidence': confidence,
            'alignment': alignment,
            'decision': decision
        }

        # Generate mock performance data
        equity_data = []
        price_data = []

        # Generate data points for the last 24 hours
        start_time = now - timedelta(hours=24)
        base_equity = 500
        base_price = 0.15

        for i in range(24 * 60):  # One point per minute
            timestamp = start_time + timedelta(minutes=i)
            unix_timestamp = timestamp.timestamp()

            # Generate equity value with some randomness
            equity = base_equity * (1 + random.uniform(-0.1, 0.1))
            equity_data.append({
                'timestamp': unix_timestamp,
                'value': equity
            })

            # Generate price value with some randomness
            price = base_price * (1 + random.uniform(-0.05, 0.05))
            price_data.append({
                'timestamp': unix_timestamp,
                'value': price
            })

        # Generate mock trade markers
        trade_markers = []
        for i in range(random.randint(5, 10)):
            timestamp = start_time + timedelta(minutes=random.randint(0, 24 * 60))
            unix_timestamp = timestamp.timestamp()

            # Entry marker
            direction = random.choice(['long', 'short'])
            price = base_price * (1 + random.uniform(-0.05, 0.05))

            trade_markers.append({
                'timestamp': unix_timestamp,
                'price': price,
                'type': 'entry',
                'direction': direction
            })

            # Exit marker (a bit later)
            exit_timestamp = timestamp + timedelta(minutes=random.randint(5, 120))
            unix_exit_timestamp = exit_timestamp.timestamp()
            exit_price = price * (1 + random.uniform(-0.03, 0.03))

            # Calculate PnL
            if direction == 'long':
                pnl = f"+{(exit_price / price - 1) * 100:.2f}%" if exit_price > price else f"{(exit_price / price - 1) * 100:.2f}%"
            else:
                pnl = f"+{(price / exit_price - 1) * 100:.2f}%" if exit_price < price else f"{(price / exit_price - 1) * 100:.2f}%"

            trade_markers.append({
                'timestamp': unix_exit_timestamp,
                'price': exit_price,
                'type': 'exit',
                'direction': direction,
                'pnl': pnl
            })

        performance_data = {
            'equity_data': equity_data,
            'price_data': price_data,
            'trade_markers': trade_markers
        }

        # Define market regime types
        regime_types = ['high_volatility', 'low_volatility', 'range_bound', 'strong_trend', 'weak_trend', 'uncertain']

        # Randomly select a primary regime
        primary_regime = random.choice(regime_types)

        # Generate regime scores
        regime_scores = {}
        total_score = 0

        for regime in regime_types:
            if regime == primary_regime:
                score = random.uniform(0.6, 0.9)
            else:
                score = random.uniform(0.0, 0.3)
            regime_scores[regime] = score
            total_score += score

        # Normalize scores
        for regime in regime_types:
            regime_scores[regime] /= total_score

        # Generate regime metrics
        trend_strength = random.uniform(-0.8, 0.8)
        volatility = random.uniform(0.2, 3.0)
        trend_alignment = random.uniform(0.2, 0.9)

        # Generate regime adjustments
        leverage_factor = 1.0
        position_size_factor = 1.0
        stop_loss_factor = 1.0
        take_profit_factor = 1.0
        entry_confidence = 0.7

        if primary_regime == 'high_volatility':
            leverage_factor = random.uniform(0.4, 0.6)
            position_size_factor = random.uniform(0.5, 0.7)
            stop_loss_factor = random.uniform(1.3, 1.7)
            take_profit_factor = random.uniform(1.3, 1.7)
            entry_confidence = random.uniform(0.75, 0.85)
        elif primary_regime == 'low_volatility':
            leverage_factor = random.uniform(0.8, 1.0)
            position_size_factor = random.uniform(0.8, 1.0)
            stop_loss_factor = random.uniform(0.7, 0.9)
            take_profit_factor = random.uniform(0.7, 0.9)
            entry_confidence = random.uniform(0.55, 0.65)
        elif primary_regime == 'range_bound':
            leverage_factor = random.uniform(0.6, 0.8)
            position_size_factor = random.uniform(0.7, 0.9)
            stop_loss_factor = random.uniform(1.1, 1.3)
            take_profit_factor = random.uniform(0.7, 0.9)
            entry_confidence = random.uniform(0.65, 0.75)
        elif primary_regime == 'strong_trend':
            leverage_factor = random.uniform(0.9, 1.1)
            position_size_factor = random.uniform(0.9, 1.1)
            stop_loss_factor = random.uniform(0.9, 1.1)
            take_profit_factor = random.uniform(1.1, 1.3)
            entry_confidence = random.uniform(0.65, 0.75)
        elif primary_regime == 'weak_trend':
            leverage_factor = random.uniform(0.7, 0.9)
            position_size_factor = random.uniform(0.8, 1.0)
            stop_loss_factor = random.uniform(0.9, 1.1)
            take_profit_factor = random.uniform(0.9, 1.1)
            entry_confidence = random.uniform(0.55, 0.65)
        else:  # uncertain
            leverage_factor = random.uniform(0.5, 0.7)
            position_size_factor = random.uniform(0.6, 0.8)
            stop_loss_factor = random.uniform(1.2, 1.4)
            take_profit_factor = random.uniform(0.9, 1.1)
            entry_confidence = random.uniform(0.7, 0.8)

        regime_data = {
            'primary_regime': primary_regime,
            'regime_scores': regime_scores,
            'metrics': {
                'trend_strength': trend_strength,
                'volatility': volatility,
                'trend_alignment': trend_alignment
            },
            'adjustments': {
                'leverage_factor': leverage_factor,
                'position_size_factor': position_size_factor,
                'stop_loss_factor': stop_loss_factor,
                'take_profit_factor': take_profit_factor,
                'entry_confidence': entry_confidence
            }
        }

        # Generate mock timeframe data
        timeframes = ['1m', '5m', '15m']
        timeframe_analysis = {}

        for tf in timeframes:
            # Generate trend direction
            if tf == '1m':
                trend_direction = random.choice(['strong_bullish', 'bullish', 'neutral', 'bearish', 'strong_bearish'])
            elif tf == '5m':
                trend_direction = random.choice(['bullish', 'neutral', 'bearish'])
            else:  # 15m
                trend_direction = random.choice(['bullish', 'neutral', 'bearish'])

            # Generate trend score
            trend_score = random.uniform(-0.8, 0.8)

            # Generate RSI
            rsi = random.uniform(30, 70)
            if rsi > 70:
                rsi_signal = 'overbought'
            elif rsi < 30:
                rsi_signal = 'oversold'
            elif rsi > 50:
                rsi_signal = 'bullish'
            else:
                rsi_signal = 'bearish'

            # Generate MACD
            macd_signal = random.choice(['bullish', 'bearish'])
            macd_hist_signal = random.choice(['bullish', 'bearish'])

            # Generate volatility
            bb_width = random.uniform(0.5, 2.0)

            timeframe_analysis[tf] = {
                'trend_direction': trend_direction,
                'trend_score': trend_score,
                'rsi': rsi,
                'rsi_signal': rsi_signal,
                'macd_signal': macd_signal,
                'macd_hist_signal': macd_hist_signal,
                'volatility': random.uniform(0.2, 2.0),
                'bb_width': bb_width
            }

        # Generate overall trend metrics
        trend_strength = (
            timeframe_analysis['1m']['trend_score'] * 0.2 +
            timeframe_analysis['5m']['trend_score'] * 0.3 +
            timeframe_analysis['15m']['trend_score'] * 0.5
        )

        # Determine overall trend direction
        if trend_strength > 0.6:
            trend_direction = "strong_bullish"
        elif trend_strength > 0.2:
            trend_direction = "bullish"
        elif trend_strength > -0.2:
            trend_direction = "neutral"
        elif trend_strength > -0.6:
            trend_direction = "bearish"
        else:
            trend_direction = "strong_bearish"

        # Calculate trend alignment
        directions = [timeframe_analysis[tf]['trend_direction'] for tf in timeframes]
        most_common = max(set(directions), key=directions.count)
        trend_alignment = directions.count(most_common) / len(directions)

        # Determine signal quality
        if len(timeframes) >= 3:
            signal_quality = 'high'
        elif len(timeframes) == 2:
            signal_quality = 'medium'
        else:
            signal_quality = 'low'

        # Generate timeframe correlation
        timeframe_correlation = {}
        for i, tf1 in enumerate(timeframes):
            for j, tf2 in enumerate(timeframes):
                if i == j:
                    correlation = 1.0
                else:
                    # Generate random correlation, higher for adjacent timeframes
                    if abs(i - j) == 1:
                        correlation = random.uniform(0.5, 0.9)
                    else:
                        correlation = random.uniform(0.2, 0.6)

                timeframe_correlation[f"{tf1}_{tf2}"] = correlation

        timeframe_data = {
            'timeframe_analysis': timeframe_analysis,
            'trend_metrics': {
                'trend_strength': trend_strength,
                'trend_direction': trend_direction,
                'trend_alignment': trend_alignment,
                'signal_quality': signal_quality,
                'timeframes_available': len(timeframes),
                'volatility': (timeframe_analysis['1m']['volatility'] +
                              timeframe_analysis['5m']['volatility'] +
                              timeframe_analysis['15m']['volatility']) / 3
            },
            'timeframe_correlation': timeframe_correlation
        }

        # Generate mock log messages
        log_messages = []

        # Add some random log messages
        message_types = [
            {'level': 'info', 'message': f"Fetched market data for {self.parameters['symbol']}"},
            {'level': 'info', 'message': f"Calculated signals: MACD={macd_score:.4f}, OB={ob_score:.4f}, VOL={vol_score:.4f}"},
            {'level': 'info', 'message': f"Detected {primary_regime} market regime with volatility {volatility:.2f}%"},
            {'level': 'info', 'message': f"Decision: {decision} with confidence {confidence:.2f}%"}
        ]

        # Add a warning or error occasionally
        if random.random() > 0.8:
            if random.random() > 0.5:
                message_types.append({'level': 'warning', 'message': "Signal alignment below threshold"})
            else:
                message_types.append({'level': 'error', 'message': "Failed to fetch order book data"})

        # Add a debug message occasionally
        if random.random() > 0.7:
            message_types.append({'level': 'debug', 'message': f"Raw API response: {{'status': 'ok', 'data': {{...}}}}"})

        # Select a few random messages
        for _ in range(random.randint(1, 3)):
            msg_type = random.choice(message_types)
            log_messages.append({
                'level': msg_type['level'],
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': msg_type['message']
            })

        # Return all mock data
        return {
            'account_balance': account_balance,
            'active_positions': active_positions,
            'position_history': position_history,
            'signal_data': signal_data,
            'performance_data': performance_data,
            'regime_data': regime_data,
            'timeframe_data': timeframe_data,
            'log_messages': log_messages
        }

    def get_trading_context(self):
        """Get current trading context for LLM analysis"""
        try:
            from datetime import datetime

            # Get current symbol and price
            symbol = self.parameters.get('symbol', 'DOGE/USDT')
            current_price = 0.0

            # Try to get current price from current state
            if 'current_price' in self.current_state:
                current_price = self.current_state['current_price']
            elif 'spot_ohlcv' in self.current_state and self.current_state['spot_ohlcv']:
                # Get latest price from OHLCV data
                current_price = self.current_state['spot_ohlcv'][-1]['close']

            # Get account balance
            account_balance = self.account_balance if hasattr(self, 'account_balance') else 1000.0

            # Get open positions
            open_positions = self.current_state.get('active_positions', [])

            # Get market data
            market_data = {
                'price': current_price,
                'symbol': symbol,
                'timestamp': datetime.now(),
                'ohlcv': self.current_state.get('spot_ohlcv', []),
                'orderbook': self.current_state.get('orderbook', {}),
                'volume': self.current_state.get('volume_24h', 0)
            }

            # Get performance metrics
            performance_metrics = {
                'total_trades': len(self.current_state.get('trade_history', [])),
                'win_rate': 0.0,  # Calculate if needed
                'total_pnl': 0.0  # Calculate if needed
            }

            # Create trading context
            trading_context = {
                'symbol': symbol,
                'current_price': current_price,
                'account_balance': account_balance,
                'open_positions': open_positions,
                'market_data': market_data,
                'performance_metrics': performance_metrics,
                'emergency_flags': [],
                'timestamp': datetime.now()
            }

            return trading_context

        except Exception as e:
            logger.error(f"Error getting trading context: {e}")
            # Return a minimal context to prevent errors
            return {
                'symbol': 'DOGE/USDT',
                'current_price': 0.0,
                'account_balance': 1000.0,
                'open_positions': [],
                'market_data': {},
                'performance_metrics': {},
                'emergency_flags': [],
                'timestamp': datetime.now()
            }


class BacktestWorker(QRunnable):
    """Worker for running backtest in background"""

    def __init__(self, params):
        super().__init__()
        self.params = params
        self.signals = WorkerSignals()

    def run(self):
        """Run the worker"""
        try:
            # Import backtesting module
            from backtesting import run_backtest

            # Extract parameters
            symbol = self.params.get('symbol', 'DOGE/USDT')
            start_date = self.params.get('start_date')
            end_date = self.params.get('end_date')
            strategy = self.params.get('strategy', 'default')
            leverage = self.params.get('leverage', 1)
            initial_balance = self.params.get('initial_balance', 1000)

            # Report progress
            self.signals.progress.emit(10)

            # Run backtest
            backtest_results = run_backtest(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                strategy=strategy,
                leverage=leverage,
                initial_balance=initial_balance
            )

            # Report progress
            self.signals.progress.emit(90)

            # Format results
            result = self.format_backtest_results(backtest_results)

            # Report completion
            self.signals.progress.emit(100)

            # Return results
            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(str(e))

    def generate_performance_data(self, current_price):
        """Generate performance data for display"""
        # For now, we'll use mock data until we have real performance data
        return self.generate_mock_backtest_results()

    def format_backtest_results(self, backtest_results):
        """Format backtest results for display"""
        from datetime import datetime

        # Extract summary metrics
        initial_balance = backtest_results.get('initial_balance', 1000)
        final_balance = backtest_results.get('final_balance', 0)
        profit = final_balance - initial_balance
        profit_pct = (profit / initial_balance * 100) if initial_balance > 0 else 0
        total_trades = backtest_results.get('total_trades', 0)
        winning_trades = backtest_results.get('winning_trades', 0)
        losing_trades = total_trades - winning_trades
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        max_drawdown = backtest_results.get('max_drawdown', 0)
        sharpe_ratio = backtest_results.get('sharpe_ratio', 0)

        # Format equity data
        equity_data = []
        if 'equity_curve' in backtest_results:
            for timestamp, value in backtest_results['equity_curve'].items():
                equity_data.append({
                    'timestamp': float(timestamp),
                    'value': float(value)
                })

        # Format trade history
        trade_history = []
        if 'trades' in backtest_results:
            for trade in backtest_results['trades']:
                trade_history.append({
                    'id': trade.get('id', ''),
                    'symbol': trade.get('symbol', ''),
                    'side': trade.get('side', ''),
                    'size': trade.get('size', 0),
                    'entry_price': trade.get('entry_price', 0),
                    'exit_price': trade.get('exit_price', 0),
                    'realized_pnl': trade.get('realized_pnl', 0),
                    'realized_pnl_pct': trade.get('realized_pnl_pct', 0),
                    'entry_time': trade.get('entry_time', ''),
                    'exit_time': trade.get('exit_time', ''),
                    'duration': trade.get('duration', '')
                })

        # Generate trade markers for equity chart
        trade_markers = []
        for trade in trade_history:
            # Entry marker
            try:
                entry_timestamp = datetime.strptime(trade['entry_time'], '%Y-%m-%d %H:%M:%S').timestamp() * 1000
                trade_markers.append({
                    'timestamp': entry_timestamp,
                    'price': trade['entry_price'],
                    'type': 'entry',
                    'direction': trade['side'].lower()
                })

                # Exit marker
                exit_timestamp = datetime.strptime(trade['exit_time'], '%Y-%m-%d %H:%M:%S').timestamp() * 1000
                trade_markers.append({
                    'timestamp': exit_timestamp,
                    'price': trade['exit_price'],
                    'type': 'exit',
                    'direction': trade['side'].lower(),
                    'pnl': f"{trade['realized_pnl_pct']:.2f}%"
                })
            except Exception as e:
                print(f"Error creating trade marker: {e}")

        # Create summary
        summary = f"Profit: {profit_pct:.2f}%, Win Rate: {win_rate:.2f}%, Trades: {total_trades}"

        # Return formatted results
        return {
            'equity_data': equity_data,
            'trade_history': trade_history,
            'trade_markers': trade_markers,
            'summary': summary
        }

    def generate_mock_backtest_results(self):
        """Generate mock backtest results"""
        import random
        from datetime import datetime, timedelta

        # Start with initial balance
        initial_balance = 500
        final_balance = initial_balance * (1 + random.uniform(-0.2, 0.5))

        # Calculate performance metrics
        total_trades = random.randint(20, 50)
        winning_trades = random.randint(10, total_trades)
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades * 100

        profit = final_balance - initial_balance
        profit_pct = profit / initial_balance * 100

        max_drawdown = random.uniform(5, 20)
        sharpe_ratio = random.uniform(0.5, 2.5)

        # Generate equity curve
        equity_data = []

        # Start and end dates from params
        start_date = datetime.strptime(self.params['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(self.params['end_date'], '%Y-%m-%d')

        # Generate daily equity points
        current_date = start_date
        current_equity = initial_balance

        while current_date <= end_date:
            # Add some randomness to equity
            current_equity *= (1 + random.uniform(-0.02, 0.03))

            equity_data.append({
                'timestamp': current_date.timestamp(),
                'value': current_equity
            })

            current_date += timedelta(days=1)

        # Ensure final equity matches final balance
        equity_data[-1]['value'] = final_balance

        # Generate trade history
        trade_history = []

        for i in range(total_trades):
            # Random entry date between start and end
            entry_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))

            # Random exit date after entry
            max_days = min(14, (end_date - entry_date).days)
            exit_date = entry_date + timedelta(days=random.randint(1, max(1, max_days)))

            # Random trade details
            side = random.choice(['LONG', 'SHORT'])
            size = random.uniform(0.1, 1.0)
            entry_price = random.uniform(0.15, 0.16)

            # Determine if winning or losing trade
            is_winning = i < winning_trades

            if is_winning:
                if side == 'LONG':
                    exit_price = entry_price * (1 + random.uniform(0.01, 0.1))
                else:
                    exit_price = entry_price * (1 - random.uniform(0.01, 0.1))
            else:
                if side == 'LONG':
                    exit_price = entry_price * (1 - random.uniform(0.01, 0.1))
                else:
                    exit_price = entry_price * (1 + random.uniform(0.01, 0.1))

            # Calculate PnL
            if side == 'LONG':
                pnl = (exit_price - entry_price) * size
                pnl_pct = (exit_price / entry_price - 1) * 100
            else:
                pnl = (entry_price - exit_price) * size
                pnl_pct = (entry_price / exit_price - 1) * 100

            trade_history.append({
                'id': f"bt_{i}",
                'symbol': self.params['symbol'],
                'side': side,
                'size': size,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'realized_pnl': pnl,
                'realized_pnl_pct': pnl_pct,
                'entry_time': entry_date.strftime('%Y-%m-%d %H:%M:%S'),
                'exit_time': exit_date.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': str(exit_date - entry_date).split('.')[0]
            })

        # Sort trade history by entry time
        trade_history.sort(key=lambda x: x['entry_time'])

        # Generate trade markers for equity chart
        trade_markers = []

        for trade in trade_history:
            # Entry marker
            entry_timestamp = datetime.strptime(trade['entry_time'], '%Y-%m-%d %H:%M:%S').timestamp()

            trade_markers.append({
                'timestamp': entry_timestamp,
                'price': trade['entry_price'],
                'type': 'entry',
                'direction': trade['side'].lower()
            })

            # Exit marker
            exit_timestamp = datetime.strptime(trade['exit_time'], '%Y-%m-%d %H:%M:%S').timestamp()

            trade_markers.append({
                'timestamp': exit_timestamp,
                'price': trade['exit_price'],
                'type': 'exit',
                'direction': trade['side'].lower(),
                'pnl': f"{trade['realized_pnl_pct']:.2f}%"
            })

        # Summary text
        summary = f"Balance: ${final_balance:.2f} | Profit: ${profit:.2f} ({profit_pct:.2f}%) | Trades: {total_trades} | Win Rate: {win_rate:.2f}% | Max DD: {max_drawdown:.2f}% | Sharpe: {sharpe_ratio:.2f}"

        return {
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'profit': profit,
            'profit_pct': profit_pct,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'equity_data': equity_data,
            'trade_history': trade_history,
            'trade_markers': trade_markers,
            'summary': summary
        }


class ExportWorker(QRunnable):
    """Worker for exporting results in background"""

    def __init__(self, export_params, current_state):
        super().__init__()
        self.export_params = export_params
        self.current_state = current_state
        self.signals = WorkerSignals()

    def run(self):
        """Run the worker"""
        try:
            # Get export format
            export_format = self.export_params['format']

            # Generate file path
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_path = f"export_{timestamp}.{export_format.lower()}"

            # Export based on format
            if export_format == 'CSV':
                self.export_to_csv(file_path)
            elif export_format == 'JSON':
                self.export_to_json(file_path)
            elif export_format == 'PNG':
                self.export_to_png(file_path)
            else:
                raise ValueError(f"Unsupported export format: {export_format}")

            # Return result
            self.signals.result.emit({
                'file_path': file_path,
                'format': export_format
            })
        except Exception as e:
            self.signals.error.emit(str(e))

    def export_to_csv(self, file_path):
        """Export data to CSV"""
        import csv

        # Determine what to export
        export_type = self.export_params.get('export_type', 'trades')

        if export_type == 'trades':
            # Export trade history
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow([
                    'ID', 'Symbol', 'Side', 'Size', 'Entry Price', 'Exit Price',
                    'PnL', 'PnL %', 'Entry Time', 'Exit Time', 'Duration'
                ])

                # Write active positions
                for position in self.current_state.get('active_positions', []):
                    writer.writerow([
                        position.get('id', ''),
                        position.get('symbol', ''),
                        position.get('side', ''),
                        position.get('size', 0),
                        position.get('entry_price', 0),
                        position.get('current_price', 0),
                        position.get('unrealized_pnl', 0),
                        position.get('unrealized_pnl_pct', 0),
                        position.get('entry_time', ''),
                        'Active',
                        'Active'
                    ])

                # Write position history
                for position in self.current_state.get('position_history', []):
                    writer.writerow([
                        position.get('id', ''),
                        position.get('symbol', ''),
                        position.get('side', ''),
                        position.get('size', 0),
                        position.get('entry_price', 0),
                        position.get('exit_price', 0),
                        position.get('realized_pnl', 0),
                        position.get('realized_pnl_pct', 0),
                        position.get('entry_time', ''),
                        position.get('exit_time', ''),
                        position.get('duration', '')
                    ])

        elif export_type == 'performance':
            # Export performance data
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow(['Timestamp', 'Balance'])

                # Write equity data
                for point in self.current_state.get('performance_data', {}).get('equity_data', []):
                    timestamp = datetime.fromtimestamp(point.get('timestamp', 0)).strftime('%Y-%m-%d %H:%M:%S')
                    writer.writerow([timestamp, point.get('value', 0)])

        elif export_type == 'signals':
            # Export signal data
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)

                # Write header
                writer.writerow([
                    'Signal', 'Value', 'Weight', 'Contribution'
                ])

                # Get signal data
                signals = self.current_state.get('signal_data', {}).get('signals', {})

                # Get weights from export params
                weights = self.export_params.get('weights', {})

                # Write signals
                for signal, value in signals.items():
                    if signal != 'total_score':
                        weight = weights.get(signal, 0)
                        contribution = value * weight
                        writer.writerow([signal, value, weight, contribution])

                # Write total
                writer.writerow(['total_score', signals.get('total_score', 0), '', ''])

    def export_to_json(self, file_path):
        """Export data to JSON"""
        import json

        # Determine what to export
        export_type = self.export_params.get('export_type', 'all')

        if export_type == 'all':
            # Export all data
            with open(file_path, 'w') as f:
                json.dump(self.current_state, f, indent=4)
        else:
            # Export specific data
            data = {}

            if export_type == 'trades':
                data['active_positions'] = self.current_state.get('active_positions', [])
                data['position_history'] = self.current_state.get('position_history', [])
            elif export_type == 'performance':
                data['performance_data'] = self.current_state.get('performance_data', {})
            elif export_type == 'signals':
                data['signal_data'] = self.current_state.get('signal_data', {})
            elif export_type == 'regime':
                data['regime_data'] = self.current_state.get('regime_data', {})
            elif export_type == 'timeframe':
                data['timeframe_data'] = self.current_state.get('timeframe_data', {})

            with open(file_path, 'w') as f:
                json.dump(data, f, indent=4)

    def export_to_png(self, file_path):
        """Export chart to PNG"""
        # This would normally use a chart rendering library
        # For now, just create a dummy image
        from PIL import Image, ImageDraw

        # Create a blank image
        img = Image.new('RGB', (800, 600), color=(255, 255, 255))
        draw = ImageDraw.Draw(img)

        # Add some text
        draw.text((10, 10), "Trading System Export", fill=(0, 0, 0))
        draw.text((10, 30), f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", fill=(0, 0, 0))

        # Save the image
        img.save(file_path)


class ClosePositionWorker(QRunnable):
    """Worker for closing a position in background"""

    def __init__(self, position):
        super().__init__()
        self.position = position
        self.signals = WorkerSignals()

    def run(self):
        """Run the worker"""
        try:
            # This would connect to your actual trading system
            # For now, simulate closing a position
            time.sleep(1)  # Simulate work

            # Create a copy of the position
            result = dict(self.position)

            # Update with exit details
            result['exit_price'] = result['current_price']
            result['exit_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Calculate duration
            entry_time = datetime.strptime(result['entry_time'], '%Y-%m-%d %H:%M:%S')
            exit_time = datetime.strptime(result['exit_time'], '%Y-%m-%d %H:%M:%S')
            result['duration'] = str(exit_time - entry_time).split('.')[0]  # Format as HH:MM:SS

            # Rename fields
            result['realized_pnl'] = result.pop('unrealized_pnl', 0)
            result['realized_pnl_pct'] = result.pop('unrealized_pnl_pct', 0)

            # Remove unnecessary fields
            result.pop('current_price', None)
            result.pop('take_profit', None)
            result.pop('stop_loss', None)

            # Return result
            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(str(e))