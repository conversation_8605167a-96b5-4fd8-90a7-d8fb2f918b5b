#!/usr/bin/env python3
"""
Test script to verify the get_trading_context method works correctly.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from trading.trading_system_interface import EpinnoxTradingInterface
    print("✓ Successfully imported EpinnoxTradingInterface")
    
    # Create an instance
    interface = EpinnoxTradingInterface()
    print("✓ Successfully created EpinnoxTradingInterface instance")
    
    # Test the get_trading_context method
    context = interface.get_trading_context()
    print("✓ Successfully called get_trading_context method")
    
    # Print the context
    print("\nTrading Context:")
    print(f"  Symbol: {context.get('symbol')}")
    print(f"  Current Price: {context.get('current_price')}")
    print(f"  Account Balance: {context.get('account_balance')}")
    print(f"  Open Positions: {len(context.get('open_positions', []))}")
    print(f"  Timestamp: {context.get('timestamp')}")
    
    print("\n✅ All tests passed! The get_trading_context method is working correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
