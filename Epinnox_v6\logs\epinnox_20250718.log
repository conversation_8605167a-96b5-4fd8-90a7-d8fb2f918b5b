2025-07-18 21:33:01,540 - main - INFO - Epinnox v6 starting up...
2025-07-18 21:33:01,557 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 21:33:01,558 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 21:33:01,558 - main - INFO - Performance monitoring initialized
2025-07-18 21:33:01,568 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:33:01,569 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 21:33:01,570 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 21:33:05,986 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 21:40:28,631 - main - INFO - Epinnox v6 starting up...
2025-07-18 21:40:28,646 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 21:40:28,647 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 21:40:28,647 - main - INFO - Performance monitoring initialized
2025-07-18 21:40:28,656 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:40:28,656 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 21:40:28,657 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 21:40:33,560 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 21:40:37,078 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-18 21:40:37,079 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:40:37,080 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:40:37,080 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 21:40:37,590 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 21:40:38,444 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 21:40:39,688 - websocket - INFO - Websocket connected
2025-07-18 21:40:42,578 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 21:40:42,978 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 21:40:42,978 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 21:40:42,978 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 21:40:42,978 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 21:40:42,984 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 21:40:45,040 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 21:40:45,041 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 21:40:45,042 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 21:40:45,048 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 21:40:45,048 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 21:40:45,048 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:40:45,049 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:40:45,049 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 21:40:45,052 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 21:40:45,069 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 21:40:45,069 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 21:40:45,069 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 21:40:45,074 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_214045_e75ca945
2025-07-18 21:40:45,074 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_214045_e75ca945
2025-07-18 21:40:45,210 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 21:40:45,212 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 21:40:45,215 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 21:40:45,217 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 21:40:45,218 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 21:40:45,218 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 21:40:45,218 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 21:40:45,219 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 21:40:45,219 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:40:46,964 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 0/60 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 0/48 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 0/32 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:41:17,836 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:41:17,836 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:42:23,176 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:42:23,176 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:43:25,713 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:44:27,865 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
