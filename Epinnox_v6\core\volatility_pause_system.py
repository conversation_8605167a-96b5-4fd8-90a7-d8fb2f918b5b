"""
Volatility-Based Trading Pause System
Automatically pauses scalping during high volatility periods and adjusts risk parameters
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import deque
from enum import Enum

logger = logging.getLogger(__name__)

class VolatilityState(Enum):
    """Volatility state classifications"""
    NORMAL = "NORMAL"
    ELEVATED = "ELEVATED"
    HIGH = "HIGH"
    EXTREME = "EXTREME"
    PAUSE_REQUIRED = "PAUSE_REQUIRED"

@dataclass
class VolatilityMetrics:
    """Volatility analysis metrics"""
    current_atr: float
    baseline_atr: float
    atr_ratio: float
    realized_volatility: float
    volume_spike_factor: float
    price_velocity: float
    volatility_state: VolatilityState
    pause_recommended: bool
    risk_adjustment: float

@dataclass
class TradingAdjustments:
    """Trading parameter adjustments based on volatility"""
    position_size_multiplier: float
    confidence_threshold_adjustment: float
    spread_threshold_multiplier: float
    max_hold_time_adjustment: float
    emergency_stop_tightening: float
    pause_trading: bool
    resume_conditions: Dict[str, Any]

class VolatilityPauseSystem:
    """Advanced volatility monitoring and trading pause system"""

    def __init__(self, lookback_hours: int = 4):
        self.lookback_hours = lookback_hours

        # ATR calculation parameters
        self.atr_periods = 14
        self.baseline_window_hours = 1

        # 🚨 CRITICAL: Data requirements for reliable analysis
        self.MIN_CANDLES_VOLATILITY = 60  # 4x ATR14 period for reliable calculation
        self.MIN_BASELINE_PERIODS = 72    # 1-hour baseline (12 x 5min periods) + buffer
        self.MIN_TRADE_HISTORY = 100      # Minimum trades for volume analysis
        
        # Volatility thresholds
        self.thresholds = {
            'elevated': 1.2,    # 1.2x baseline ATR
            'high': 1.5,        # 1.5x baseline ATR
            'extreme': 2.0,     # 2.0x baseline ATR
            'pause': 2.5        # 2.5x baseline ATR - pause trading
        }
        
        # Recovery conditions
        self.recovery_thresholds = {
            'atr_ratio': 1.3,           # Must drop below 1.3x baseline
            'consecutive_periods': 3,    # For 3 consecutive 5-minute periods
            'volume_normalization': 1.5  # Volume must normalize to <1.5x average
        }
        
        # Historical data storage
        self.atr_history = deque(maxlen=288)  # 24 hours of 5-minute ATR values
        self.volume_history = deque(maxlen=288)
        self.price_history = deque(maxlen=1440)  # 24 hours of 1-minute prices
        
        # State tracking
        self.current_state = VolatilityState.NORMAL
        self.pause_start_time = None
        self.consecutive_normal_periods = 0
        self.last_analysis_time = None
        
        # Performance tracking
        self.pause_events = []
        self.adjustment_history = []
        
    def analyze_volatility(self, symbol: str, market_data: Dict[str, Any]) -> VolatilityMetrics:
        """
        Analyze current volatility conditions and determine trading adjustments
        
        Args:
            symbol: Trading symbol
            market_data: Market data including OHLCV and volume
            
        Returns:
            VolatilityMetrics with complete volatility analysis
        """
        try:
            # Extract market data
            candles_5m = market_data.get('candles_5m', [])
            candles_1m = market_data.get('candles_1m', [])
            recent_trades = market_data.get('recent_trades', [])
            current_volume = market_data.get('volume_24h', 0)

            # 🚨 CRITICAL: Check data sufficiency before analysis
            if not candles_5m or len(candles_5m) < self.MIN_CANDLES_VOLATILITY:
                logger.warning(f"Insufficient 5m data for volatility analysis: {len(candles_5m)}/{self.MIN_CANDLES_VOLATILITY} for {symbol}")
                return self._get_default_metrics()

            if not candles_1m or len(candles_1m) < 60:  # Need 1 hour of 1m data
                logger.warning(f"Insufficient 1m data for volatility analysis: {len(candles_1m)}/60 for {symbol}")
                return self._get_default_metrics()

            if len(recent_trades) < self.MIN_TRADE_HISTORY:
                logger.warning(f"Insufficient trade history for volume analysis: {len(recent_trades)}/{self.MIN_TRADE_HISTORY} for {symbol}")
                # Continue with reduced functionality but log the limitation
            
            # Calculate current ATR
            current_atr = self._calculate_atr(candles_5m)
            
            # Calculate baseline ATR (1-hour rolling average)
            baseline_atr = self._calculate_baseline_atr()
            
            # Calculate ATR ratio
            atr_ratio = current_atr / baseline_atr if baseline_atr > 0 else 1.0
            
            # Calculate realized volatility
            realized_volatility = self._calculate_realized_volatility(candles_1m)
            
            # Calculate volume spike factor
            volume_spike_factor = self._calculate_volume_spike_factor(current_volume)
            
            # Calculate price velocity
            price_velocity = self._calculate_price_velocity(candles_1m)
            
            # Determine volatility state
            volatility_state = self._classify_volatility_state(atr_ratio, volume_spike_factor, price_velocity)
            
            # Determine if pause is recommended
            pause_recommended = self._should_pause_trading(volatility_state, atr_ratio)
            
            # Calculate risk adjustment factor
            risk_adjustment = self._calculate_risk_adjustment(volatility_state, atr_ratio)
            
            # Store historical data
            self._update_historical_data(current_atr, current_volume, candles_1m)
            
            # Update state tracking
            self._update_state_tracking(volatility_state)
            
            metrics = VolatilityMetrics(
                current_atr=current_atr,
                baseline_atr=baseline_atr,
                atr_ratio=atr_ratio,
                realized_volatility=realized_volatility,
                volume_spike_factor=volume_spike_factor,
                price_velocity=price_velocity,
                volatility_state=volatility_state,
                pause_recommended=pause_recommended,
                risk_adjustment=risk_adjustment
            )
            
            self.last_analysis_time = datetime.now()
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error in volatility analysis for {symbol}: {e}")
            return self._get_default_metrics()
    
    def get_trading_adjustments(self, volatility_metrics: VolatilityMetrics) -> TradingAdjustments:
        """
        Get specific trading parameter adjustments based on volatility
        
        Args:
            volatility_metrics: Current volatility analysis
            
        Returns:
            TradingAdjustments with specific parameter modifications
        """
        try:
            state = volatility_metrics.volatility_state
            atr_ratio = volatility_metrics.atr_ratio
            
            # Base adjustments
            adjustments = TradingAdjustments(
                position_size_multiplier=1.0,
                confidence_threshold_adjustment=0.0,
                spread_threshold_multiplier=1.0,
                max_hold_time_adjustment=1.0,
                emergency_stop_tightening=1.0,
                pause_trading=False,
                resume_conditions={}
            )
            
            # Adjust based on volatility state
            if state == VolatilityState.ELEVATED:
                adjustments.position_size_multiplier = 0.8  # Reduce position size by 20%
                adjustments.confidence_threshold_adjustment = 5.0  # Require 5% higher confidence
                adjustments.spread_threshold_multiplier = 0.8  # Require tighter spreads
                
            elif state == VolatilityState.HIGH:
                adjustments.position_size_multiplier = 0.6  # Reduce position size by 40%
                adjustments.confidence_threshold_adjustment = 10.0  # Require 10% higher confidence
                adjustments.spread_threshold_multiplier = 0.6  # Require much tighter spreads
                adjustments.max_hold_time_adjustment = 0.7  # Reduce max hold time by 30%
                adjustments.emergency_stop_tightening = 1.5  # Tighten emergency stops by 50%
                
            elif state == VolatilityState.EXTREME:
                adjustments.position_size_multiplier = 0.4  # Reduce position size by 60%
                adjustments.confidence_threshold_adjustment = 15.0  # Require 15% higher confidence
                adjustments.spread_threshold_multiplier = 0.4  # Very tight spread requirements
                adjustments.max_hold_time_adjustment = 0.5  # Reduce max hold time by 50%
                adjustments.emergency_stop_tightening = 2.0  # Double emergency stop sensitivity
                
            elif state == VolatilityState.PAUSE_REQUIRED:
                adjustments.pause_trading = True
                adjustments.resume_conditions = {
                    'atr_ratio_below': self.recovery_thresholds['atr_ratio'],
                    'consecutive_periods': self.recovery_thresholds['consecutive_periods'],
                    'volume_normalization': self.recovery_thresholds['volume_normalization']
                }
            
            # Fine-tune based on exact ATR ratio
            if not adjustments.pause_trading:
                # Additional position size scaling based on ATR ratio
                if atr_ratio > 1.0:
                    additional_scaling = max(0.3, 1.0 - (atr_ratio - 1.0) * 0.5)
                    adjustments.position_size_multiplier *= additional_scaling
                
                # Additional confidence adjustment
                if atr_ratio > 1.2:
                    additional_confidence = (atr_ratio - 1.2) * 10
                    adjustments.confidence_threshold_adjustment += additional_confidence
            
            # Store adjustment history
            self.adjustment_history.append({
                'timestamp': datetime.now(),
                'volatility_state': state,
                'atr_ratio': atr_ratio,
                'adjustments': adjustments
            })
            
            # Keep only recent history
            if len(self.adjustment_history) > 1000:
                self.adjustment_history = self.adjustment_history[-500:]
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Error calculating trading adjustments: {e}")
            return TradingAdjustments(1.0, 0.0, 1.0, 1.0, 1.0, False, {})
    
    def check_resume_conditions(self, current_metrics: VolatilityMetrics) -> bool:
        """
        Check if conditions are met to resume trading after a pause
        
        Args:
            current_metrics: Current volatility metrics
            
        Returns:
            True if trading can be resumed
        """
        try:
            if not self.pause_start_time:
                return True  # Not currently paused
            
            # Check ATR ratio condition
            if current_metrics.atr_ratio > self.recovery_thresholds['atr_ratio']:
                self.consecutive_normal_periods = 0
                return False
            
            # Check volume normalization
            if current_metrics.volume_spike_factor > self.recovery_thresholds['volume_normalization']:
                self.consecutive_normal_periods = 0
                return False
            
            # Increment consecutive normal periods
            self.consecutive_normal_periods += 1
            
            # Check if we have enough consecutive normal periods
            if self.consecutive_normal_periods >= self.recovery_thresholds['consecutive_periods']:
                logger.info(f"Resume conditions met after {self.consecutive_normal_periods} consecutive normal periods")
                self._record_pause_end()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking resume conditions: {e}")
            return False
    
    def _calculate_atr(self, candles: List[Dict]) -> float:
        """Calculate Average True Range"""
        try:
            if len(candles) < self.atr_periods:
                return 0.0
            
            true_ranges = []
            for i in range(1, len(candles)):
                high = float(candles[i]['high'])
                low = float(candles[i]['low'])
                prev_close = float(candles[i-1]['close'])
                
                tr = max(
                    high - low,
                    abs(high - prev_close),
                    abs(low - prev_close)
                )
                true_ranges.append(tr)
            
            # Calculate ATR as simple moving average of true ranges
            if len(true_ranges) >= self.atr_periods:
                atr = np.mean(true_ranges[-self.atr_periods:])
            else:
                atr = np.mean(true_ranges)
            
            return float(atr)
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return 0.0
    
    def _calculate_baseline_atr(self) -> float:
        """Calculate baseline ATR from historical data"""
        try:
            # 🚨 CRITICAL: Ensure sufficient data for reliable baseline
            if len(self.atr_history) < self.MIN_BASELINE_PERIODS:
                logger.warning(f"Insufficient ATR history for baseline: {len(self.atr_history)}/{self.MIN_BASELINE_PERIODS}")
                return 0.001  # Default baseline

            # Use 1-hour rolling average as baseline (12 * 5min periods)
            baseline_periods = min(12, len(self.atr_history))
            baseline_atr = np.mean(list(self.atr_history)[-baseline_periods:])

            return float(baseline_atr)
            
        except Exception as e:
            logger.error(f"Error calculating baseline ATR: {e}")
            return 0.001
    
    def _calculate_realized_volatility(self, candles_1m: List[Dict]) -> float:
        """Calculate realized volatility from 1-minute returns"""
        try:
            if len(candles_1m) < 60:  # Need at least 1 hour of data
                return 0.0
            
            closes = [float(c['close']) for c in candles_1m[-60:]]  # Last hour
            returns = np.diff(closes) / closes[:-1]
            
            # Annualized volatility
            volatility = np.std(returns) * np.sqrt(365 * 24 * 60)  # 365 days * 24 hours * 60 minutes
            
            return float(volatility)
            
        except Exception as e:
            logger.error(f"Error calculating realized volatility: {e}")
            return 0.0
    
    def _calculate_volume_spike_factor(self, current_volume: float) -> float:
        """Calculate volume spike factor vs historical average"""
        try:
            if len(self.volume_history) < 12:
                return 1.0
            
            historical_avg = np.mean(list(self.volume_history)[-12:])  # Last hour average
            
            if historical_avg > 0:
                spike_factor = current_volume / historical_avg
            else:
                spike_factor = 1.0
            
            return float(spike_factor)
            
        except Exception as e:
            logger.error(f"Error calculating volume spike factor: {e}")
            return 1.0
    
    def _calculate_price_velocity(self, candles_1m: List[Dict]) -> float:
        """Calculate price change velocity (price changes per minute)"""
        try:
            if len(candles_1m) < 10:
                return 0.0
            
            closes = [float(c['close']) for c in candles_1m[-10:]]  # Last 10 minutes
            
            # Count significant price changes (>0.1%)
            price_changes = 0
            for i in range(1, len(closes)):
                change_pct = abs(closes[i] - closes[i-1]) / closes[i-1]
                if change_pct > 0.001:  # >0.1% change
                    price_changes += 1
            
            # Velocity = changes per minute
            velocity = price_changes / len(closes)
            
            return float(velocity)
            
        except Exception as e:
            logger.error(f"Error calculating price velocity: {e}")
            return 0.0
    
    def _classify_volatility_state(self, atr_ratio: float, volume_spike: float, price_velocity: float) -> VolatilityState:
        """Classify current volatility state"""
        try:
            # Primary classification based on ATR ratio
            if atr_ratio >= self.thresholds['pause']:
                return VolatilityState.PAUSE_REQUIRED
            elif atr_ratio >= self.thresholds['extreme']:
                return VolatilityState.EXTREME
            elif atr_ratio >= self.thresholds['high']:
                return VolatilityState.HIGH
            elif atr_ratio >= self.thresholds['elevated']:
                return VolatilityState.ELEVATED
            
            # Check for volume/velocity spikes that might indicate emerging volatility
            if volume_spike > 3.0 or price_velocity > 0.8:
                if atr_ratio > 1.1:
                    return VolatilityState.ELEVATED
            
            return VolatilityState.NORMAL
            
        except Exception as e:
            logger.error(f"Error classifying volatility state: {e}")
            return VolatilityState.NORMAL
    
    def _should_pause_trading(self, state: VolatilityState, atr_ratio: float) -> bool:
        """Determine if trading should be paused"""
        return state == VolatilityState.PAUSE_REQUIRED or atr_ratio >= self.thresholds['pause']
    
    def _calculate_risk_adjustment(self, state: VolatilityState, atr_ratio: float) -> float:
        """Calculate overall risk adjustment multiplier"""
        try:
            if state == VolatilityState.PAUSE_REQUIRED:
                return 0.0  # No trading
            elif state == VolatilityState.EXTREME:
                return 0.3
            elif state == VolatilityState.HIGH:
                return 0.5
            elif state == VolatilityState.ELEVATED:
                return 0.7
            else:
                return 1.0
            
        except Exception as e:
            logger.error(f"Error calculating risk adjustment: {e}")
            return 1.0
    
    def _update_historical_data(self, current_atr: float, current_volume: float, candles_1m: List[Dict]):
        """Update historical data storage"""
        try:
            self.atr_history.append(current_atr)
            self.volume_history.append(current_volume)
            
            # Update price history with recent 1m closes
            if candles_1m:
                recent_closes = [float(c['close']) for c in candles_1m[-10:]]
                self.price_history.extend(recent_closes)
            
        except Exception as e:
            logger.error(f"Error updating historical data: {e}")
    
    def _update_state_tracking(self, new_state: VolatilityState):
        """Update state tracking and record transitions"""
        try:
            if new_state != self.current_state:
                logger.info(f"Volatility state transition: {self.current_state} → {new_state}")
                
                # Record pause start
                if new_state == VolatilityState.PAUSE_REQUIRED and not self.pause_start_time:
                    self.pause_start_time = datetime.now()
                    self.consecutive_normal_periods = 0
                    logger.warning("Trading pause initiated due to extreme volatility")
                
                self.current_state = new_state
            
        except Exception as e:
            logger.error(f"Error updating state tracking: {e}")
    
    def _record_pause_end(self):
        """Record the end of a trading pause"""
        try:
            if self.pause_start_time:
                pause_duration = datetime.now() - self.pause_start_time
                
                pause_event = {
                    'start_time': self.pause_start_time,
                    'end_time': datetime.now(),
                    'duration_minutes': pause_duration.total_seconds() / 60,
                    'consecutive_normal_periods': self.consecutive_normal_periods
                }
                
                self.pause_events.append(pause_event)
                
                logger.info(f"Trading pause ended after {pause_duration.total_seconds()/60:.1f} minutes")
                
                self.pause_start_time = None
                self.consecutive_normal_periods = 0
            
        except Exception as e:
            logger.error(f"Error recording pause end: {e}")
    
    def _get_default_metrics(self) -> VolatilityMetrics:
        """Get default volatility metrics when analysis fails"""
        return VolatilityMetrics(
            current_atr=0.001,
            baseline_atr=0.001,
            atr_ratio=1.0,
            realized_volatility=0.5,
            volume_spike_factor=1.0,
            price_velocity=0.0,
            volatility_state=VolatilityState.NORMAL,
            pause_recommended=False,
            risk_adjustment=1.0
        )
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get volatility pause system statistics"""
        try:
            total_pause_time = sum(event['duration_minutes'] for event in self.pause_events)
            
            return {
                'current_state': self.current_state.value,
                'is_paused': self.pause_start_time is not None,
                'pause_start_time': self.pause_start_time.isoformat() if self.pause_start_time else None,
                'consecutive_normal_periods': self.consecutive_normal_periods,
                'total_pause_events': len(self.pause_events),
                'total_pause_time_minutes': total_pause_time,
                'atr_history_size': len(self.atr_history),
                'volume_history_size': len(self.volume_history),
                'last_analysis_time': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
                'adjustment_history_size': len(self.adjustment_history)
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
