#!/usr/bin/env python3
"""
ScalperGPT GUI Integration Tests
Comprehensive testing for the dynamic GUI interface
"""

import sys
import os
import unittest
import time
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5 imports for testing
try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtTest import QTest
    from PyQt5.QtCore import Qt
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("⚠️ PyQt5 not available for testing")

class TestParameterDiscovery(unittest.TestCase):
    """Test parameter discovery functionality"""
    
    def setUp(self):
        """Setup test environment"""
        if PYQT_AVAILABLE:
            from scalper_gui import ParameterDiscovery
            self.discovery = ParameterDiscovery()
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_parameter_discovery(self):
        """Test parameter discovery from various sources"""
        params = self.discovery.discover_all_parameters()
        
        # Should discover some parameters
        self.assertGreater(len(params), 0, "Should discover at least some parameters")
        
        # Check parameter structure
        for param_id, param_info in params.items():
            self.assertIsInstance(param_id, str)
            self.assertTrue(hasattr(param_info, 'name'))
            self.assertTrue(hasattr(param_info, 'param_type'))
            self.assertTrue(hasattr(param_info, 'category'))
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_dataclass_discovery(self):
        """Test discovery from dataclass configurations"""
        self.discovery._discover_from_dataclasses()
        
        # Should have discovered some dataclass parameters
        dataclass_params = [p for p in self.discovery.discovered_parameters.values() 
                           if 'trading' in p.category.lower() or 'risk' in p.category.lower()]
        self.assertGreater(len(dataclass_params), 0)
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_yaml_discovery(self):
        """Test discovery from YAML configuration files"""
        self.discovery._discover_from_yaml_configs()
        
        # Should have discovered some YAML parameters
        yaml_params = [p for p in self.discovery.discovered_parameters.values() 
                      if '.yaml' in p.category]
        # Note: This might be 0 if no YAML files exist, which is okay
        self.assertGreaterEqual(len(yaml_params), 0)

class TestWidgetFactory(unittest.TestCase):
    """Test widget factory functionality"""
    
    def setUp(self):
        """Setup test environment"""
        if PYQT_AVAILABLE:
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
            
            from scalper_gui import WidgetFactory, ParameterInfo
            self.factory = WidgetFactory
            self.ParameterInfo = ParameterInfo
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_boolean_widget_creation(self):
        """Test creation of boolean widgets"""
        param_info = self.ParameterInfo(
            name="test_bool",
            param_type=bool,
            default_value=True,
            description="Test Boolean",
            category="Test"
        )
        
        widget = self.factory.create_widget(param_info)
        self.assertIsNotNone(widget)
        
        # Should be a checkbox
        from PyQt5.QtWidgets import QCheckBox
        self.assertIsInstance(widget, QCheckBox)
        self.assertTrue(widget.isChecked())
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_numeric_widget_creation(self):
        """Test creation of numeric widgets"""
        # Integer parameter
        int_param = self.ParameterInfo(
            name="test_int",
            param_type=int,
            default_value=10,
            description="Test Integer",
            category="Test",
            min_value=0,
            max_value=100
        )
        
        int_widget = self.factory.create_widget(int_param)
        self.assertIsNotNone(int_widget)
        
        # Float parameter
        float_param = self.ParameterInfo(
            name="test_float",
            param_type=float,
            default_value=0.5,
            description="Test Float",
            category="Test",
            min_value=0.0,
            max_value=1.0
        )
        
        float_widget = self.factory.create_widget(float_param)
        self.assertIsNotNone(float_widget)
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_choice_widget_creation(self):
        """Test creation of choice widgets"""
        param_info = self.ParameterInfo(
            name="test_choice",
            param_type=str,
            default_value="option1",
            description="Test Choice",
            category="Test",
            choices=["option1", "option2", "option3"]
        )
        
        widget = self.factory.create_widget(param_info)
        self.assertIsNotNone(widget)
        
        # Should be a combobox
        from PyQt5.QtWidgets import QComboBox
        self.assertIsInstance(widget, QComboBox)
        self.assertEqual(widget.count(), 3)

class TestLiveBindingSystem(unittest.TestCase):
    """Test live binding system functionality"""
    
    def setUp(self):
        """Setup test environment"""
        if PYQT_AVAILABLE:
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
            
            from scalper_gui import LiveBindingSystem
            self.binding_system = LiveBindingSystem()
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_component_registration(self):
        """Test trading system component registration"""
        mock_component = Mock()
        mock_component.config = Mock()
        mock_component.config.test_param = "initial_value"
        
        self.binding_system.register_trading_system_component("test_component", mock_component)
        
        self.assertIn("test_component", self.binding_system.trading_system_refs)
        self.assertEqual(self.binding_system.trading_system_refs["test_component"], mock_component)
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_parameter_binding(self):
        """Test parameter binding functionality"""
        # Register mock component
        mock_component = Mock()
        mock_component.config = Mock()
        mock_component.config.test_param = "initial_value"
        
        self.binding_system.register_trading_system_component("test_component", mock_component)
        
        # Bind parameter
        self.binding_system.bind_parameter(
            "test_param_id", 
            "test_component", 
            "config.test_param"
        )
        
        self.assertIn("test_param_id", self.binding_system.parameter_bindings)
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_parameter_update(self):
        """Test live parameter updates"""
        # Register mock component
        mock_component = Mock()
        mock_component.config = Mock()
        mock_component.config.test_param = "initial_value"
        
        self.binding_system.register_trading_system_component("test_component", mock_component)
        self.binding_system.bind_parameter("test_param_id", "test_component", "config.test_param")
        
        # Update parameter
        success = self.binding_system.update_parameter("test_param_id", "new_value")
        
        self.assertTrue(success)
        self.assertEqual(mock_component.config.test_param, "new_value")

class TestScalperGUIIntegration(unittest.TestCase):
    """Test full ScalperGPT GUI integration"""
    
    def setUp(self):
        """Setup test environment"""
        if PYQT_AVAILABLE:
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_main_window_creation(self):
        """Test main window creation and initialization"""
        from scalper_gui import ScalperGPTMainWindow
        
        # Create main window
        main_window = ScalperGPTMainWindow()
        
        # Check basic properties
        self.assertIsNotNone(main_window)
        self.assertEqual(main_window.windowTitle(), "ScalperGPT - Dynamic Trading Interface")
        
        # Check components are initialized
        self.assertIsNotNone(main_window.parameter_discovery)
        self.assertIsNotNone(main_window.live_binding)
        self.assertIsNotNone(main_window.hot_reload)
        
        # Check UI components
        self.assertIsNotNone(main_window.config_tabs)
        self.assertGreater(main_window.config_tabs.count(), 0)
        
        main_window.close()
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_configuration_panels(self):
        """Test configuration panel functionality"""
        from scalper_gui import ScalperGPTMainWindow
        
        main_window = ScalperGPTMainWindow()
        
        # Check all panels are created
        expected_panels = ['symbols_data', 'llm_prompts', 'execution_rules', 
                          'risk_management', 'monitoring_logging']
        
        for panel_key in expected_panels:
            self.assertIn(panel_key, main_window.config_panels)
            panel = main_window.config_panels[panel_key]
            self.assertIsNotNone(panel)
        
        main_window.close()
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    @patch('scalper_gui.EpinnoxTradingInterface')
    def test_trading_system_connection(self, mock_trading_interface):
        """Test trading system connection"""
        from scalper_gui import ScalperGPTMainWindow
        
        # Setup mock
        mock_instance = Mock()
        mock_trading_interface._instance = mock_instance
        
        main_window = ScalperGPTMainWindow()
        
        # Test connection
        main_window.connect_trading_system()
        
        # Should have set trading interface
        self.assertEqual(main_window.trading_interface, mock_instance)
        
        main_window.close()

class TestErrorHandling(unittest.TestCase):
    """Test error handling and edge cases"""
    
    def setUp(self):
        """Setup test environment"""
        if PYQT_AVAILABLE:
            self.app = QApplication.instance()
            if self.app is None:
                self.app = QApplication([])
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_missing_config_files(self):
        """Test handling of missing configuration files"""
        from scalper_gui import ParameterDiscovery
        
        discovery = ParameterDiscovery()
        
        # Should not crash with missing files
        discovery._discover_from_yaml_configs()
        
        # Should still work
        self.assertIsInstance(discovery.discovered_parameters, dict)
    
    @unittest.skipUnless(PYQT_AVAILABLE, "PyQt5 not available")
    def test_invalid_parameter_binding(self):
        """Test handling of invalid parameter bindings"""
        from scalper_gui import LiveBindingSystem
        
        binding_system = LiveBindingSystem()
        
        # Try to update non-existent parameter
        success = binding_system.update_parameter("non_existent", "value")
        self.assertFalse(success)
        
        # Try to bind to non-existent component
        binding_system.bind_parameter("test", "non_existent_component", "attr")
        success = binding_system.update_parameter("test", "value")
        self.assertFalse(success)

def run_integration_tests():
    """Run all integration tests"""
    print("🧪 Running ScalperGPT GUI Integration Tests...")
    
    if not PYQT_AVAILABLE:
        print("❌ PyQt5 not available - skipping GUI tests")
        return False
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_cases = [
        TestParameterDiscovery,
        TestWidgetFactory,
        TestLiveBindingSystem,
        TestScalperGUIIntegration,
        TestErrorHandling
    ]
    
    for test_case in test_cases:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_case)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n📊 Test Results:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return len(result.failures) == 0 and len(result.errors) == 0

def validate_gui_functionality():
    """Validate GUI functionality without full test suite"""
    print("🔍 Validating GUI functionality...")
    
    try:
        # Test parameter discovery
        from scalper_gui import ParameterDiscovery
        discovery = ParameterDiscovery()
        params = discovery.discover_all_parameters()
        print(f"✅ Parameter discovery: {len(params)} parameters found")
        
        # Test widget factory
        from scalper_gui import WidgetFactory, ParameterInfo
        test_param = ParameterInfo(
            name="test", param_type=bool, default_value=True,
            description="Test", category="Test"
        )
        
        if PYQT_AVAILABLE:
            app = QApplication.instance() or QApplication([])
            widget = WidgetFactory.create_widget(test_param)
            print("✅ Widget factory: Widget creation successful")
        else:
            print("⚠️ Widget factory: PyQt5 not available")
        
        # Test live binding
        from scalper_gui import LiveBindingSystem
        binding = LiveBindingSystem()
        print("✅ Live binding: System initialization successful")
        
        print("✅ All core functionality validated")
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--validate":
        # Quick validation
        success = validate_gui_functionality()
        sys.exit(0 if success else 1)
    else:
        # Full test suite
        success = run_integration_tests()
        sys.exit(0 if success else 1)
