"""
Multi-Timeframe Regime Detection System for Crypto Futures Trading
Analyzes 1m/5m/15m timeframes to classify market regimes and optimize trading strategies
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import deque

logger = logging.getLogger(__name__)

class RegimeType(Enum):
    """Market regime classifications"""
    TRENDING_STRONG = "TRENDING_STRONG"
    TRENDING_WEAK = "TRENDING_WEAK"
    RANGING_TIGHT = "RANGING_TIGHT"
    RANGING_VOLATILE = "RANGING_VOLATILE"
    BREAKOUT_PENDING = "BREAKOUT_PENDING"
    REVERSAL_ZONE = "REVERSAL_ZONE"
    UNKNOWN = "UNKNOWN"

@dataclass
class TimeframeAnalysis:
    """Analysis for a specific timeframe"""
    timeframe: str
    trend_strength: float  # 0-1 scale
    volatility: float
    momentum: float
    volume_trend: float
    support_resistance_strength: float
    regime: RegimeType
    confidence: float

@dataclass
class RegimeAnalysis:
    """Complete multi-timeframe regime analysis"""
    symbol: str
    timestamp: datetime
    primary_regime: RegimeType
    regime_confidence: float
    timeframe_analysis: Dict[str, TimeframeAnalysis]
    regime_alignment: float  # How aligned are the timeframes (0-1)
    trading_bias: str  # 'BULLISH', 'BEARISH', 'NEUTRAL'
    optimal_strategy: str  # 'MOMENTUM', 'MEAN_REVERSION', 'BREAKOUT', 'WAIT'
    risk_adjustment: float  # Risk multiplier based on regime

class RegimeDetector:
    """Multi-timeframe regime detection and analysis"""
    
    def __init__(self):
        self.timeframes = ['1m', '5m', '15m']

        # 🚨 CRITICAL: Minimum data requirements for reliable analysis
        self.MINIMUM_CANDLES = {
            '1m': 60,   # 1 hour for micro-trend analysis
            '5m': 48,   # 4 hours for tactical analysis
            '15m': 32   # 8 hours for strategic analysis
        }

        # Lookback periods for analysis (same as minimum for now)
        self.lookback_periods = self.MINIMUM_CANDLES.copy()
        
        # Historical regime tracking
        self.regime_history = deque(maxlen=100)
        self.regime_transitions = deque(maxlen=50)
        
        # Performance tracking
        self.analysis_count = 0
        self.last_analysis_time = None
        
    def detect_regime(self, symbol: str, market_data: Dict[str, Any]) -> Optional[RegimeAnalysis]:
        """
        Detect market regime across multiple timeframes
        
        Args:
            symbol: Trading symbol
            market_data: Market data including OHLCV for multiple timeframes
            
        Returns:
            RegimeAnalysis with complete regime classification
        """
        try:
            start_time = datetime.now()

            # 🚨 CRITICAL: Check data sufficiency for each timeframe
            data_sufficient = True
            for tf in self.timeframes:
                tf_data = market_data.get(f'candles_{tf}', [])
                min_required = self.MINIMUM_CANDLES[tf]

                if len(tf_data) < min_required:
                    logger.warning(f"Insufficient {tf} data for regime detection: {len(tf_data)}/{min_required} for {symbol}")
                    data_sufficient = False

            if not data_sufficient:
                logger.warning(f"Insufficient data across timeframes for reliable regime detection: {symbol}")
                return None

            # Analyze each timeframe
            timeframe_results = {}
            for tf in self.timeframes:
                tf_data = market_data.get(f'candles_{tf}', [])
                analysis = self._analyze_timeframe(tf, tf_data)
                if analysis:
                    timeframe_results[tf] = analysis

            if not timeframe_results:
                logger.warning(f"No valid timeframe analysis for regime detection: {symbol}")
                return None
            
            # Determine primary regime from timeframe consensus
            primary_regime, regime_confidence = self._determine_primary_regime(timeframe_results)
            
            # Calculate regime alignment
            regime_alignment = self._calculate_regime_alignment(timeframe_results)
            
            # Determine trading bias and optimal strategy
            trading_bias = self._determine_trading_bias(timeframe_results)
            optimal_strategy = self._determine_optimal_strategy(primary_regime, regime_alignment)
            
            # Calculate risk adjustment factor
            risk_adjustment = self._calculate_risk_adjustment(primary_regime, regime_alignment)
            
            # Create comprehensive analysis
            analysis = RegimeAnalysis(
                symbol=symbol,
                timestamp=start_time,
                primary_regime=primary_regime,
                regime_confidence=regime_confidence,
                timeframe_analysis=timeframe_results,
                regime_alignment=regime_alignment,
                trading_bias=trading_bias,
                optimal_strategy=optimal_strategy,
                risk_adjustment=risk_adjustment
            )
            
            # Store in history and track transitions
            self._update_regime_history(analysis)
            
            # Update performance tracking
            self.analysis_count += 1
            self.last_analysis_time = datetime.now()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in regime detection for {symbol}: {e}")
            return None
    
    def _analyze_timeframe(self, timeframe: str, candles: List[Dict]) -> Optional[TimeframeAnalysis]:
        """Analyze a specific timeframe for regime characteristics"""
        try:
            min_required = self.MINIMUM_CANDLES[timeframe]
            if len(candles) < min_required:
                logger.warning(f"Insufficient {timeframe} candles for analysis: {len(candles)}/{min_required}")
                return None
            
            # Extract OHLCV data
            closes = np.array([float(c['close']) for c in candles[-self.lookback_periods[timeframe]:]])
            highs = np.array([float(c['high']) for c in candles[-self.lookback_periods[timeframe]:]])
            lows = np.array([float(c['low']) for c in candles[-self.lookback_periods[timeframe]:]])
            volumes = np.array([float(c['volume']) for c in candles[-self.lookback_periods[timeframe]:]])
            
            # Calculate trend strength
            trend_strength = self._calculate_trend_strength(closes)
            
            # Calculate volatility
            volatility = self._calculate_volatility(closes)
            
            # Calculate momentum
            momentum = self._calculate_momentum(closes)
            
            # Calculate volume trend
            volume_trend = self._calculate_volume_trend(volumes)
            
            # Calculate support/resistance strength
            sr_strength = self._calculate_support_resistance_strength(highs, lows, closes)
            
            # Classify regime for this timeframe
            regime = self._classify_regime(trend_strength, volatility, momentum, sr_strength)
            
            # Calculate confidence in regime classification
            confidence = self._calculate_regime_confidence(trend_strength, volatility, momentum)
            
            return TimeframeAnalysis(
                timeframe=timeframe,
                trend_strength=trend_strength,
                volatility=volatility,
                momentum=momentum,
                volume_trend=volume_trend,
                support_resistance_strength=sr_strength,
                regime=regime,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error analyzing timeframe {timeframe}: {e}")
            return None
    
    def _calculate_trend_strength(self, closes: np.ndarray) -> float:
        """Calculate trend strength (0-1 scale)"""
        try:
            if len(closes) < 10:
                return 0.0
            
            # Linear regression slope
            x = np.arange(len(closes))
            slope, _ = np.polyfit(x, closes, 1)
            
            # Normalize slope by price
            normalized_slope = abs(slope) / closes[-1] * len(closes)
            
            # R-squared for trend consistency
            correlation = np.corrcoef(x, closes)[0, 1] ** 2
            
            # Combine slope magnitude and consistency
            trend_strength = min(1.0, normalized_slope * 10) * correlation
            
            return float(trend_strength)
            
        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return 0.0
    
    def _calculate_volatility(self, closes: np.ndarray) -> float:
        """Calculate normalized volatility"""
        try:
            if len(closes) < 2:
                return 0.0
            
            # Calculate returns
            returns = np.diff(closes) / closes[:-1]
            
            # Annualized volatility (assuming crypto trades 24/7)
            volatility = np.std(returns) * np.sqrt(365 * 24 * 60)  # Annualized
            
            return float(volatility)
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0
    
    def _calculate_momentum(self, closes: np.ndarray) -> float:
        """Calculate price momentum"""
        try:
            if len(closes) < 10:
                return 0.0
            
            # Rate of change over different periods
            roc_short = (closes[-1] - closes[-5]) / closes[-5] if len(closes) >= 5 else 0
            roc_medium = (closes[-1] - closes[-10]) / closes[-10] if len(closes) >= 10 else 0
            roc_long = (closes[-1] - closes[-20]) / closes[-20] if len(closes) >= 20 else 0
            
            # Weighted momentum (recent periods have higher weight)
            momentum = (roc_short * 0.5 + roc_medium * 0.3 + roc_long * 0.2)
            
            return float(momentum)
            
        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0
    
    def _calculate_volume_trend(self, volumes: np.ndarray) -> float:
        """Calculate volume trend"""
        try:
            if len(volumes) < 10:
                return 0.0
            
            # Compare recent volume to historical average
            recent_avg = np.mean(volumes[-5:])
            historical_avg = np.mean(volumes[:-5])
            
            if historical_avg > 0:
                volume_trend = (recent_avg - historical_avg) / historical_avg
            else:
                volume_trend = 0.0
            
            return float(volume_trend)
            
        except Exception as e:
            logger.error(f"Error calculating volume trend: {e}")
            return 0.0
    
    def _calculate_support_resistance_strength(self, highs: np.ndarray, 
                                             lows: np.ndarray, closes: np.ndarray) -> float:
        """Calculate support/resistance strength"""
        try:
            if len(closes) < 20:
                return 0.0
            
            current_price = closes[-1]
            
            # Find potential support/resistance levels
            price_range = np.max(highs) - np.min(lows)
            tolerance = price_range * 0.01  # 1% tolerance
            
            # Count touches near current price level
            touches = 0
            for price in np.concatenate([highs, lows]):
                if abs(price - current_price) <= tolerance:
                    touches += 1
            
            # Normalize by total data points
            sr_strength = min(1.0, touches / len(closes) * 5)
            
            return float(sr_strength)
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance strength: {e}")
            return 0.0
    
    def _classify_regime(self, trend_strength: float, volatility: float, 
                        momentum: float, sr_strength: float) -> RegimeType:
        """Classify market regime based on calculated metrics"""
        try:
            # Strong trending conditions
            if trend_strength > 0.7 and abs(momentum) > 0.02:
                return RegimeType.TRENDING_STRONG
            
            # Weak trending conditions
            elif trend_strength > 0.4 and abs(momentum) > 0.01:
                return RegimeType.TRENDING_WEAK
            
            # Ranging conditions
            elif trend_strength < 0.3:
                if volatility < 0.5:
                    return RegimeType.RANGING_TIGHT
                else:
                    return RegimeType.RANGING_VOLATILE
            
            # Breakout pending (low volatility + strong support/resistance)
            elif volatility < 0.3 and sr_strength > 0.6:
                return RegimeType.BREAKOUT_PENDING
            
            # Reversal zone (high volatility + momentum divergence)
            elif volatility > 1.0 and trend_strength > 0.5:
                return RegimeType.REVERSAL_ZONE
            
            return RegimeType.UNKNOWN
            
        except Exception as e:
            logger.error(f"Error classifying regime: {e}")
            return RegimeType.UNKNOWN
    
    def _calculate_regime_confidence(self, trend_strength: float, 
                                   volatility: float, momentum: float) -> float:
        """Calculate confidence in regime classification"""
        try:
            # Base confidence from trend clarity
            base_confidence = trend_strength * 0.6
            
            # Volatility consistency bonus
            if 0.2 <= volatility <= 1.0:  # Optimal volatility range
                base_confidence += 0.2
            
            # Momentum consistency bonus
            if abs(momentum) > 0.005:  # Meaningful momentum
                base_confidence += 0.2
            
            return min(1.0, base_confidence)
            
        except Exception as e:
            logger.error(f"Error calculating regime confidence: {e}")
            return 0.5
    
    def _determine_primary_regime(self, timeframe_results: Dict[str, TimeframeAnalysis]) -> Tuple[RegimeType, float]:
        """Determine primary regime from timeframe consensus"""
        try:
            # Weight timeframes by importance (shorter = higher weight for scalping)
            weights = {'1m': 0.5, '5m': 0.3, '15m': 0.2}
            
            regime_scores = {}
            total_weight = 0
            
            for tf, analysis in timeframe_results.items():
                weight = weights.get(tf, 0.1)
                regime = analysis.regime
                confidence = analysis.confidence
                
                if regime not in regime_scores:
                    regime_scores[regime] = 0
                
                regime_scores[regime] += weight * confidence
                total_weight += weight
            
            if not regime_scores:
                return RegimeType.UNKNOWN, 0.5
            
            # Find regime with highest weighted score
            primary_regime = max(regime_scores, key=regime_scores.get)
            regime_confidence = regime_scores[primary_regime] / total_weight
            
            return primary_regime, regime_confidence
            
        except Exception as e:
            logger.error(f"Error determining primary regime: {e}")
            return RegimeType.UNKNOWN, 0.5
    
    def _calculate_regime_alignment(self, timeframe_results: Dict[str, TimeframeAnalysis]) -> float:
        """Calculate how aligned the timeframes are"""
        try:
            if len(timeframe_results) < 2:
                return 1.0
            
            regimes = [analysis.regime for analysis in timeframe_results.values()]
            
            # Count regime matches
            regime_counts = {}
            for regime in regimes:
                regime_counts[regime] = regime_counts.get(regime, 0) + 1
            
            # Calculate alignment as percentage of matching regimes
            max_count = max(regime_counts.values())
            alignment = max_count / len(regimes)
            
            return float(alignment)
            
        except Exception as e:
            logger.error(f"Error calculating regime alignment: {e}")
            return 0.5
    
    def _determine_trading_bias(self, timeframe_results: Dict[str, TimeframeAnalysis]) -> str:
        """Determine overall trading bias"""
        try:
            total_momentum = 0
            total_weight = 0
            weights = {'1m': 0.5, '5m': 0.3, '15m': 0.2}
            
            for tf, analysis in timeframe_results.items():
                weight = weights.get(tf, 0.1)
                total_momentum += analysis.momentum * weight
                total_weight += weight
            
            if total_weight > 0:
                avg_momentum = total_momentum / total_weight
                
                if avg_momentum > 0.01:
                    return 'BULLISH'
                elif avg_momentum < -0.01:
                    return 'BEARISH'
            
            return 'NEUTRAL'
            
        except Exception as e:
            logger.error(f"Error determining trading bias: {e}")
            return 'NEUTRAL'
    
    def _determine_optimal_strategy(self, primary_regime: RegimeType, alignment: float) -> str:
        """Determine optimal trading strategy based on regime"""
        try:
            # High alignment gives more confidence in strategy
            if alignment < 0.6:
                return 'WAIT'  # Low alignment = conflicting signals
            
            if primary_regime == RegimeType.TRENDING_STRONG:
                return 'MOMENTUM'
            elif primary_regime == RegimeType.TRENDING_WEAK:
                return 'MOMENTUM'
            elif primary_regime in [RegimeType.RANGING_TIGHT, RegimeType.RANGING_VOLATILE]:
                return 'MEAN_REVERSION'
            elif primary_regime == RegimeType.BREAKOUT_PENDING:
                return 'BREAKOUT'
            elif primary_regime == RegimeType.REVERSAL_ZONE:
                return 'WAIT'
            
            return 'WAIT'
            
        except Exception as e:
            logger.error(f"Error determining optimal strategy: {e}")
            return 'WAIT'
    
    def _calculate_risk_adjustment(self, primary_regime: RegimeType, alignment: float) -> float:
        """Calculate risk adjustment multiplier"""
        try:
            base_multiplier = 1.0
            
            # Adjust based on regime
            if primary_regime == RegimeType.TRENDING_STRONG:
                base_multiplier = 1.2  # Slightly more aggressive
            elif primary_regime == RegimeType.RANGING_VOLATILE:
                base_multiplier = 0.7  # More conservative
            elif primary_regime == RegimeType.REVERSAL_ZONE:
                base_multiplier = 0.5  # Very conservative
            elif primary_regime == RegimeType.BREAKOUT_PENDING:
                base_multiplier = 0.8  # Cautious
            
            # Adjust based on alignment
            alignment_multiplier = 0.5 + (alignment * 0.5)  # 0.5-1.0 range
            
            return base_multiplier * alignment_multiplier
            
        except Exception as e:
            logger.error(f"Error calculating risk adjustment: {e}")
            return 1.0
    
    def _update_regime_history(self, analysis: RegimeAnalysis):
        """Update regime history and track transitions"""
        try:
            # Add to history
            self.regime_history.append(analysis)
            
            # Track regime transitions
            if len(self.regime_history) >= 2:
                prev_regime = self.regime_history[-2].primary_regime
                current_regime = analysis.primary_regime
                
                if prev_regime != current_regime:
                    transition = {
                        'timestamp': analysis.timestamp,
                        'from_regime': prev_regime,
                        'to_regime': current_regime,
                        'confidence': analysis.regime_confidence
                    }
                    self.regime_transitions.append(transition)
                    
                    logger.info(f"Regime transition detected: {prev_regime} → {current_regime} "
                              f"(confidence: {analysis.regime_confidence:.2f})")
            
        except Exception as e:
            logger.error(f"Error updating regime history: {e}")
    
    def get_regime_statistics(self) -> Dict[str, Any]:
        """Get regime detection statistics"""
        try:
            if not self.regime_history:
                return {}
            
            # Count regime occurrences
            regime_counts = {}
            for analysis in self.regime_history:
                regime = analysis.primary_regime
                regime_counts[regime.value] = regime_counts.get(regime.value, 0) + 1
            
            # Calculate average confidence
            avg_confidence = np.mean([a.regime_confidence for a in self.regime_history])
            
            # Calculate average alignment
            avg_alignment = np.mean([a.regime_alignment for a in self.regime_history])
            
            return {
                'total_analyses': len(self.regime_history),
                'regime_distribution': regime_counts,
                'average_confidence': float(avg_confidence),
                'average_alignment': float(avg_alignment),
                'total_transitions': len(self.regime_transitions),
                'last_analysis_time': self.last_analysis_time
            }
            
        except Exception as e:
            logger.error(f"Error getting regime statistics: {e}")
            return {}
